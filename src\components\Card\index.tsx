'use client'
import { cn } from '@/utilities/ui'
import useClickableCard from '@/utilities/useClickableCard'
import Link from 'next/link'
import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { Calendar, ArrowUpRight } from 'lucide-react'
import { ProductCard } from '@/components/ProductCard'

import type { Post, Config } from '@/payload-types'

// Temporary Product type until payload types are regenerated
type Product = {
  id: string
  title: string
  slug: string
  heroImage?: any
  categories?: any[]
  populatedAuthors?: any[]
  publishedAt?: string
  price?: number
  currency?: string
  meta?: {
    title?: string | null
    description?: string | null
    image?: any
  } | null
}

import { Media } from '@/components/Media'

export type CardPostData = Pick<Post, 'slug' | 'categories' | 'meta' | 'title' | 'publishedAt' | 'heroImage' | 'populatedAuthors'> & {
  meta?: {
    title?: string | null
    description?: string | null
    image?: any // This should include the full media object
  } | null
}

export type CardProductData = Pick<Product, 'slug' | 'categories' | 'meta' | 'title' | 'publishedAt' | 'heroImage' | 'populatedAuthors' | 'price' | 'currency'> & {
  meta?: {
    title?: string | null
    description?: string | null
    image?: any // This should include the full media object
  } | null
}

export const Card: React.FC<{
  alignItems?: 'center'
  className?: string
  doc?: CardPostData | CardProductData
  relationTo?: 'posts' | 'products'
  showCategories?: boolean
  title?: string
  locale?: string
}> = (props) => {
  const { className, doc, relationTo, showCategories, title: titleFromProps, locale = 'en' } = props

  // Use enhanced ProductCard for products
  if (relationTo === 'products' && doc) {
    return (
      <ProductCard
        product={doc as any}
        locale={locale}
        className={className}
        showQuickActions={true}
        variant="default"
      />
    )
  }

  // Continue with existing card logic for posts
  const { card, link } = useClickableCard({})
  const [isHovered, setIsHovered] = useState(false)

  const { slug, categories, meta, title, publishedAt, heroImage, populatedAuthors } = doc || {}
  const { description, image: metaImage } = meta || {}

  // Use heroImage if metaImage is not available
  const imageToUse = metaImage || heroImage



  const hasCategories = categories && Array.isArray(categories) && categories.length > 0
  const titleToUse = titleFromProps || title
  const sanitizedDescription = description?.replace(/\s/g, ' ') // replace non-breaking space with white space
  const href = `/${locale}/${relationTo}/${slug}`

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // formatPrice function removed - now handled by ProductCard component

  return (
    <motion.article
      className={cn(
        'group relative bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 cursor-pointer border border-gray-100',
        'before:absolute before:inset-0 before:bg-gradient-to-br before:from-transparent before:via-transparent before:to-red-50/30 before:opacity-0 before:transition-opacity before:duration-500 hover:before:opacity-100',
        className,
      )}
      ref={card.ref}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ y: -4 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
    >
      {/* Image Container */}
      <div className="relative w-full aspect-[4/3] overflow-hidden bg-gradient-to-br from-gray-50 via-white to-gray-100">
        {imageToUse && (typeof imageToUse === 'object' || typeof imageToUse === 'string') ? (
          <div className="relative w-full h-full">
            <Media
              resource={imageToUse}
              size="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              fill={true}
              imgClassName="object-cover transition-transform duration-700 group-hover:scale-105"
              priority={false}
            />
            {/* Product-specific overlay */}
            {relationTo === 'products' ? (
              <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-white/95 backdrop-blur-sm rounded-lg p-3 transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                    <p className="text-sm font-semibold text-gray-800">View Product Details</p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            )}
          </div>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-red-50 via-white to-red-100">
            <div className="text-center p-8">
              <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-red-100 to-red-200 rounded-3xl flex items-center justify-center shadow-lg">
                <div className="w-10 h-10 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl shadow-inner"></div>
              </div>
              <p className="text-gray-500 text-sm font-medium">No Image Available</p>
            </div>
          </div>
        )}

        {/* Product type indicator or Floating Action Button */}
        {relationTo === 'products' ? (
          <div className="absolute top-4 right-4">
            <div className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg">
              PRODUCT
            </div>
          </div>
        ) : (
          <motion.div
            className="absolute top-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg opacity-0 group-hover:opacity-100 transition-all duration-300"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{
              scale: isHovered ? 1 : 0.8,
              opacity: isHovered ? 1 : 0
            }}
            transition={{ duration: 0.2 }}
          >
            <ArrowUpRight className="w-5 h-5 text-destructive" />
          </motion.div>
        )}


      </div>

      {/* Content Container */}
      <div className="relative p-8 bg-white">
        {/* Decorative Element */}
        <div className="absolute top-0 left-8 w-16 h-1.5 bg-gradient-to-r from-red-500 to-red-300 rounded-full transform -translate-y-0.5"></div>

        {/* Categories */}
        {showCategories && hasCategories && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-2">
              {categories?.slice(0, 2).map((category, index) => {
                if (typeof category === 'object') {
                  const { title: titleFromCategory } = category
                  const categoryTitle = titleFromCategory || 'Untitled'

                  return (
                    <span
                      key={index}
                      className="px-3 py-1 bg-red-50 text-destructive text-xs font-semibold rounded-full border border-red-100"
                    >
                      {categoryTitle}
                    </span>
                  )
                }
                return null
              })}
            </div>
          </div>
        )}

        {/* Title */}
        {titleToUse && (
          <div className="mb-4">
            <h3 className="text-2xl font-bold text-gray-900 leading-tight group-hover:text-destructive transition-colors duration-300 line-clamp-2">
              <Link className="block" href={href} ref={link.ref}>
                {titleToUse}
              </Link>
            </h3>
          </div>
        )}

        {/* Description */}
        {description && (
          <div className="mb-6">
            <p className="text-gray-600 leading-relaxed text-base line-clamp-3">
              {sanitizedDescription}
            </p>
          </div>
        )}

        {/* This section is now handled by ProductCard component for products */}

        {/* Date and Action */}
        <div className='flex justify-between items-end'>
          {publishedAt && (
            <div className="flex items-center gap-2 text-gray-500 text-sm">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(publishedAt)}</span>
            </div>
          )}
          <motion.div
            className="flex items-center gap-2 text-destructive font-medium text-sm opacity-0 group-hover:opacity-100 transition-all duration-300"
            animate={{ x: isHovered ? 4 : 0 }}
          >
            <span>{relationTo === 'products' ? 'View Product' : 'Read More'}</span>
            <ArrowUpRight className="w-4 h-4" />
          </motion.div>
        </div>

      </div>

      {/* Hover Glow Effect */}
      <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-red-500/5 via-transparent to-red-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"></div>
    </motion.article>
  )
}
