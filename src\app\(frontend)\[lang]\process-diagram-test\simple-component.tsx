'use client'

import React from 'react'
import { cn } from '@/utilities/ui'

export const SimpleProcessDiagram: React.FC = () => {
  return (
    <div className="py-8">
      <h2 className="text-2xl font-bold text-center mb-4">Simple Process Diagram</h2>
      <div className="container mx-auto">
        <div className="flex flex-col space-y-8">
          {[1, 2, 3].map((step) => (
            <div 
              key={step}
              className={cn(
                "flex items-start p-4 rounded-lg border border-gray-200",
                "hover:shadow-md transition-shadow duration-300"
              )}
            >
              <div className="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full bg-primary text-white font-semibold">
                {step}
              </div>
              <div className="ml-4">
                <h3 className="text-xl font-bold">Step {step}</h3>
                <p className="text-gray-600">This is a simple description for step {step}.</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
