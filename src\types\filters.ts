/**
 * Product filter types and interfaces
 */

export interface ProductCategory {
  id: string
  title: string
  slug: string
  description?: string
  icon?: any
}

export interface FormatOption {
  label: string
  value: string
  description?: string
}

export interface ProductFilterState {
  category: string | null
  format: string | null
}

export interface ProductFilterConfig {
  categories: ProductCategory[]
  formatOptions: FormatOption[]
  locale: string
}

export interface FilteredProductsResult {
  products: any[]
  totalCount: number
  appliedFilters: ProductFilterState
}

/**
 * Kitchen format options by locale
 */
export const KITCHEN_FORMATS: Record<string, FormatOption[]> = {
  en: [
    { 
      label: 'U-shaped Kitchen', 
      value: 'u-shaped',
      description: 'Maximizes counter and storage space with three connected walls'
    },
    { 
      label: 'L-shaped Kitchen', 
      value: 'l-shaped',
      description: 'Efficient layout using two perpendicular walls'
    },
    { 
      label: 'Kitchen with Central Island', 
      value: 'central-island',
      description: 'Features a central island for additional workspace and storage'
    },
  ],
  fr: [
    { 
      label: 'Cuisine en U', 
      value: 'u-shaped',
      description: 'Maximise l\'espace de comptoir et de rangement avec trois murs connectés'
    },
    { 
      label: 'Cuisine en L', 
      value: 'l-shaped',
      description: 'Aménagement efficace utilisant deux murs perpendiculaires'
    },
    { 
      label: 'Cuisine avec îlot central', 
      value: 'central-island',
      description: 'Comprend un îlot central pour un espace de travail et de rangement supplémentaire'
    },
  ],
}

/**
 * Filter validation schemas
 */
export const VALID_CATEGORIES = [
  'cuisines',
  'dressings', 
  'salles-de-bain',
  'electromenager',
  'tables',
  'accessoires'
] as const

export const VALID_FORMATS = [
  'u-shaped',
  'l-shaped', 
  'central-island'
] as const

export type ValidCategory = typeof VALID_CATEGORIES[number]
export type ValidFormat = typeof VALID_FORMATS[number]

/**
 * Filter utility functions
 */
export function isValidCategory(category: string): category is ValidCategory {
  return VALID_CATEGORIES.includes(category as ValidCategory)
}

export function isValidFormat(format: string): format is ValidFormat {
  return VALID_FORMATS.includes(format as ValidFormat)
}

export function getFormatOptionsForCategory(
  category: string | null, 
  locale: string = 'en'
): FormatOption[] {
  // Only show format options for kitchen category
  if (category === 'cuisines') {
    return KITCHEN_FORMATS[locale] || KITCHEN_FORMATS.en
  }
  return []
}

/**
 * URL parameter names for filters
 */
export const FILTER_PARAMS = {
  CATEGORY: 'category',
  FORMAT: 'format',
} as const

/**
 * Filter analytics events
 */
export const FILTER_EVENTS = {
  CATEGORY_CHANGED: 'filter_category_changed',
  FORMAT_CHANGED: 'filter_format_changed',
  FILTERS_CLEARED: 'filters_cleared',
  FILTER_APPLIED: 'filter_applied',
} as const

/**
 * Default filter state
 */
export const DEFAULT_FILTER_STATE: ProductFilterState = {
  category: null,
  format: null,
}

/**
 * Filter state validation
 */
export function validateFilterState(state: any): ProductFilterState {
  const validated: ProductFilterState = { ...DEFAULT_FILTER_STATE }
  
  if (typeof state?.category === 'string' && isValidCategory(state.category)) {
    validated.category = state.category
  }
  
  if (typeof state?.format === 'string' && isValidFormat(state.format)) {
    // Only allow format if category is cuisines
    if (validated.category === 'cuisines') {
      validated.format = state.format
    }
  }
  
  return validated
}

/**
 * Create filter breadcrumbs for UI display
 */
export interface FilterBreadcrumb {
  type: 'category' | 'format'
  label: string
  value: string
  onRemove: () => void
}

export function createFilterBreadcrumbs(
  filters: ProductFilterState,
  config: ProductFilterConfig,
  onCategoryRemove: () => void,
  onFormatRemove: () => void
): FilterBreadcrumb[] {
  const breadcrumbs: FilterBreadcrumb[] = []
  
  if (filters.category) {
    const category = config.categories.find(cat => cat.slug === filters.category)
    if (category) {
      breadcrumbs.push({
        type: 'category',
        label: category.title,
        value: filters.category,
        onRemove: onCategoryRemove,
      })
    }
  }
  
  if (filters.format) {
    const format = config.formatOptions.find(opt => opt.value === filters.format)
    if (format) {
      breadcrumbs.push({
        type: 'format',
        label: format.label,
        value: filters.format,
        onRemove: onFormatRemove,
      })
    }
  }
  
  return breadcrumbs
}
