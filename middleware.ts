import { NextRequest, NextResponse } from 'next/server'

// Supported locales - should match your Payload CMS configuration
export const locales = ['en', 'fr'] as const
export type Locale = (typeof locales)[number]

// Default locale - should match your Payload CMS default
export const defaultLocale: Locale = 'fr'

// Get locale from pathname
function getLocaleFromPathname(pathname: string): Locale | null {
  const segments = pathname.split('/')
  const potentialLocale = segments[1]

  if (locales.includes(potentialLocale as Locale)) {
    return potentialLocale as Locale
  }

  return null
}

// Get preferred locale from Accept-Language header
function getPreferredLocale(request: NextRequest): Locale {
  // Check if user has a stored preference in cookies
  const cookieLocale = request.cookies.get('locale-preference')?.value
  if (cookieLocale && locales.includes(cookieLocale as Locale)) {
    return cookieLocale as Locale
  }

  // Check Accept-Language header
  const acceptLanguage = request.headers.get('accept-language')
  if (acceptLanguage) {
    // Parse Accept-Language header and find best match
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [code, q = '1'] = lang.trim().split(';q=')
        return { code: code?.split('-')[0] || '', quality: parseFloat(q) }
      })
      .filter(lang => lang.code) // Remove empty codes
      .sort((a, b) => b.quality - a.quality)

    for (const { code } of languages) {
      if (locales.includes(code as Locale)) {
        return code as Locale
      }
    }
  }

  return defaultLocale
}

// Check if pathname should be excluded from locale handling
function shouldExcludePathname(pathname: string): boolean {
  const excludedPaths = [
    '/api',
    '/admin',
    '/_next',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
    '/manifest.json',
  ]

  return excludedPaths.some(path => pathname.startsWith(path))
}

export function middleware(request: NextRequest) {
  const { pathname, search } = request.nextUrl

  // Skip middleware for excluded paths
  if (shouldExcludePathname(pathname)) {
    return NextResponse.next()
  }

  // Get current locale from pathname
  const currentLocale = getLocaleFromPathname(pathname)

  // If pathname already has a valid locale, continue
  if (currentLocale) {
    // Set locale cookie for future requests
    const response = NextResponse.next()
    response.cookies.set('locale-preference', currentLocale, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
      sameSite: 'strict',
    })
    return response
  }

  // If no locale in pathname, redirect to localized version
  const preferredLocale = getPreferredLocale(request)

  // Handle root path - ensure exact match
  if (pathname === '/' || pathname === '') {
    const redirectUrl = new URL(`/${preferredLocale}`, request.url)
    const response = NextResponse.redirect(redirectUrl)
    response.cookies.set('locale-preference', preferredLocale, {
      path: '/',
      maxAge: 60 * 60 * 24 * 365, // 1 year
      sameSite: 'strict',
    })
    return response
  }

  // Handle other paths without locale
  const redirectUrl = new URL(`/${preferredLocale}${pathname}${search}`, request.url)
  const response = NextResponse.redirect(redirectUrl)
  response.cookies.set('locale-preference', preferredLocale, {
    path: '/',
    maxAge: 60 * 60 * 24 * 365, // 1 year
    sameSite: 'strict',
  })
  return response
}

export const config = {
  // Match all paths except those starting with:
  // - api (API routes)
  // - _next/static (static files)
  // - _next/image (image optimization files)
  // - favicon.ico (favicon file)
  // - public files with extensions
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*|admin).*)',
  ],
}
