import type { Metada<PERSON> } from 'next'

import { RelatedPosts } from '@/blocks/RelatedPosts/Component'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import { draftMode } from 'next/headers'
import React, { cache } from 'react'
import RichText from '@/components/RichText'

import type { Post } from '@/payload-types'

import { PostHero } from '@/heros/PostHero'
import { generateMeta } from '@/utilities/generateMeta'
import PageClient from './page.client'
import { LivePreviewListener } from '@/components/LivePreviewListener'

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })
  const posts = await payload.find({
    collection: 'posts',
    draft: false,
    limit: 1000,
    overrideAccess: false,
    pagination: false,
    select: {
      slug: true,
    },
  })

  const params = posts.docs.map(({ slug }) => {
    return { slug }
  })

  return params
}

type Args = {
  params: Promise<{
    slug?: string
  }>
}

export default async function Post({ params: paramsPromise }: Args) {
  const { isEnabled: draft } = await draftMode()
  const { slug = '' } = await paramsPromise
  const url = '/posts/' + slug
  const post = await queryPostBySlug({ slug })

  if (!post) return <PayloadRedirects url={url} />

  return (
    <article>
      <PageClient />

      {/* Allows redirects for valid pages too */}
      <PayloadRedirects disableNotFound url={url} />

      {draft && <LivePreviewListener />}

      <PostHero post={post} />

      {/* Main Content Section */}
      <div className="relative bg-white">
        {/* Decorative Elements */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-red-500 to-transparent"></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
          <div className="max-w-4xl mx-auto">
            {/* Article Meta */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-12 pb-8 border-b border-gray-200">
              <div className="flex flex-wrap items-center gap-4 mb-4 sm:mb-0">
                {post.categories && post.categories.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {post.categories.slice(0, 3).map((category, index) => {
                      if (typeof category === 'object') {
                        return (
                          <span
                            key={index}
                            className="px-3 py-1 bg-red-50 text-destructive text-sm font-semibold rounded-full border border-red-100"
                          >
                            {category.title || 'Untitled'}
                          </span>
                        )
                      }
                      return null
                    })}
                  </div>
                )}
              </div>

              {post.publishedAt && (
                <div className="text-gray-500 text-sm">
                  Published on {new Date(post.publishedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </div>
              )}
            </div>

            {/* Article Content */}
            <div className="prose prose-lg lg:prose-xl max-w-none">
              <RichText
                className="leading-relaxed text-gray-800"
                data={post.content}
                enableGutter={false}
              />
            </div>

            {/* Author Section */}
            {post.populatedAuthors && post.populatedAuthors.length > 0 && (
              <div className="mt-16 pt-8 border-t border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">About the Author</h3>
                <div className="flex flex-col sm:flex-row gap-6">
                  {post.populatedAuthors.slice(0, 2).map((author, index) => {
                    if (typeof author === 'object') {
                      return (
                        <div key={index} className="flex items-center gap-4 p-6 bg-gray-50 rounded-2xl">
                          <div className="w-16 h-16 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                            <span className="text-white text-xl font-bold">
                              {author.name ? author.name.charAt(0).toUpperCase() : 'A'}
                            </span>
                          </div>
                          <div>
                            <h4 className="font-semibold text-gray-900">
                              {author.name || 'Author'}
                            </h4>
                            <p className="text-gray-600 text-sm">Content Creator</p>
                          </div>
                        </div>
                      )
                    }
                    return null
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Related Posts Section */}
      {post.relatedPosts && post.relatedPosts.length > 0 && (
        <div className="bg-gray-50 py-16 lg:py-24">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-6xl mx-auto">
              <div className="text-center mb-12">
                <div className="flex justify-center mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <div className="w-16 h-0.5 bg-gradient-to-r from-red-500 to-red-300"></div>
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-16 h-0.5 bg-gradient-to-l from-red-500 to-red-300"></div>
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  </div>
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Related Articles</h2>
                <p className="text-gray-600 text-lg">Discover more content you might enjoy</p>
              </div>

              <RelatedPosts
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                docs={post.relatedPosts.filter((post) => typeof post === 'object') as Post[]}
              />
            </div>
          </div>
        </div>
      )}
    </article>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { slug = '' } = await paramsPromise
  const post = await queryPostBySlug({ slug })

  return generateMeta({ doc: post })
}

const queryPostBySlug = cache(async ({ slug }: { slug: string }) => {
  const { isEnabled: draft } = await draftMode()

  const payload = await getPayload({ config: configPromise })

  const result = await payload.find({
    collection: 'posts',
    draft,
    limit: 1,
    depth: 2,
    overrideAccess: draft,
    pagination: false,
    where: {
      slug: {
        equals: slug,
      },
    },
  })

  return result.docs?.[0] || null
})
