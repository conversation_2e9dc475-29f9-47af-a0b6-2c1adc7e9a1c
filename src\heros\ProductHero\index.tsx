'use client'
import { formatDateTime } from 'src/utilities/formatDateTime'
import React, { useEffect } from 'react'
// cn utility removed - not used in this component
import useImageBrightness from '@/hooks/useImageBrightness'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import type { Theme } from '@/providers/Theme/types'


// Temporary Product type until payload types are regenerated
type Product = {
  id: string
  title: string
  slug: string
  heroImage?: any
  categories?: any[]
  populatedAuthors?: any[]
  publishedAt?: string
  price?: number
  currency?: string
  dimensions?: {
    width?: number
    height?: number
    depth?: number
    weight?: number
  }
}

import { Media } from '@/components/Media'
import { formatAuthors } from '@/utilities/formatAuthors'

export const ProductHero: React.FC<{
  product: Product
  enableAdaptiveTextColor?: boolean
  textTheme?: Theme
  imagePosition?: string
}> = ({
  product,
  enableAdaptiveTextColor = true,
  textTheme: initialTextTheme = 'dark',
  // imagePosition removed - not used in current implementation
}) => {
    const { categories, heroImage, populatedAuthors, publishedAt, title, dimensions } = product
    const { setHeaderTheme } = useHeaderTheme()

    // Get the image URL if heroImage is available
    const imageUrl =
      typeof heroImage === 'object' && heroImage !== null && 'url' in heroImage && typeof heroImage.url === 'string'
        ? heroImage.url
        : undefined

    // Use our custom hook to determine text color based on image brightness
    const { textTheme, isLoading } = useImageBrightness(imageUrl, {
      defaultTheme: 'dark',
      enableAdaptiveTextColor,
      fixedTextTheme: initialTextTheme
    })

    // Set header theme based on image analysis
    useEffect(() => {
      if (!isLoading) {
        setHeaderTheme(textTheme as Theme)
      }
    }, [setHeaderTheme, textTheme, isLoading])

    const hasAuthors =
      populatedAuthors && populatedAuthors.length > 0 && formatAuthors(populatedAuthors) !== ''

    // formatPrice function removed - not used in current implementation

    const formatDimensions = (dimensions: any) => {
      if (!dimensions) return null
      const { width, height, depth, weight } = dimensions
      const parts = []
      if (width && height && depth) {
        parts.push(`${width} × ${height} × ${depth} cm`)
      }
      if (weight) {
        parts.push(`${weight} kg`)
      }
      return parts.join(' • ')
    }

    return (
      <div className="relative -mt-[10.4rem] min-h-[100vh] flex items-end" data-theme="dark">
        {/* Background Image */}
        <div className="absolute inset-0 select-none">
          {heroImage && typeof heroImage !== 'string' ? (
            <div className="relative w-full h-full">
              <Media
                fill
                priority
                imgClassName="object-cover"
                resource={heroImage}
                size="100vw"
              />
              {/* Enhanced gradient overlay for better text readability */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/30" />
              <div className="absolute inset-0 bg-gradient-to-r from-black/40 via-transparent to-black/40" />
            </div>
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
              <div className="text-center text-white/50">
                <div className="w-24 h-24 mx-auto mb-4 bg-white/10 rounded-2xl flex items-center justify-center">
                  <div className="w-12 h-12 bg-white/20 rounded-xl"></div>
                </div>
                <p>No Product Image</p>
              </div>
            </div>
          )}
        </div>

        {/* Content Overlay */}
        <div className="relative z-10 w-full">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pb-16 lg:pb-24">
            <div className="max-w-4xl mx-auto text-white">

              {/* Product Badge & Categories */}
              <div className="flex flex-wrap items-center gap-3 mb-8">


                {/* Categories */}
                {categories && categories.length > 0 && (
                  <>
                    {categories.slice(0, 2).map((category, index) => {
                      if (typeof category === 'object' && category !== null) {
                        const titleToUse = category.title || 'Untitled category'
                        return (
                          <span
                            key={index}
                            className="px-4 py-2 bg-white/20 backdrop-blur-sm text-white text-sm font-semibold rounded-full border border-white/30 hover:bg-white/30 transition-all duration-200"
                          >
                            {titleToUse}
                          </span>
                        )
                      }
                      return null
                    })}
                    {categories.length > 2 && (
                      <span className="px-4 py-2 bg-white/10 backdrop-blur-sm text-white/70 text-sm font-medium rounded-full border border-white/20">
                        +{categories.length - 2} more
                      </span>
                    )}
                  </>
                )}
              </div>

              {/* Title */}
              <div className="mb-8">
                <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight mb-6">
                  {title}
                </h1>
              </div>

              {/* Enhanced Product Information Cards */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
                {/* Dimensions */}
                {dimensions && formatDimensions(dimensions) && (
                  <div className="group bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/30 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center">
                        <svg className="w-5 h-5 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <p className="text-sm text-white/70 font-medium">Dimensions</p>
                    </div>
                    <p className="text-lg font-bold text-white">{formatDimensions(dimensions)}</p>
                  </div>
                )}

                {/* Category/Format */}
                {categories && categories.length > 0 && (
                  <div className="group bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/30 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-green-500/20 rounded-xl flex items-center justify-center">
                        <svg className="w-5 h-5 text-green-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                        </svg>
                      </div>
                      <p className="text-sm text-white/70 font-medium">Category</p>
                    </div>
                    <p className="text-lg font-bold text-white">{categories[0]?.title || 'Product'}</p>
                  </div>
                )}

                {/* Designer/Author */}
                {hasAuthors && (
                  <div className="group bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/30 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-purple-500/20 rounded-xl flex items-center justify-center">
                        <svg className="w-5 h-5 text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <p className="text-sm text-white/70 font-medium">Designer</p>
                    </div>
                    <p className="text-lg font-bold text-white">{formatAuthors(populatedAuthors) || 'Unknown Designer'}</p>
                  </div>
                )}

                {/* Publication Date */}
                {publishedAt && (
                  <div className="group bg-white/15 backdrop-blur-md rounded-2xl p-6 border border-white/30 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                    <div className="flex items-center gap-3 mb-3">
                      <div className="w-10 h-10 bg-orange-500/20 rounded-xl flex items-center justify-center">
                        <svg className="w-5 h-5 text-orange-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                      <p className="text-sm text-white/70 font-medium">Published</p>
                    </div>
                    <p className="text-lg font-bold text-white">
                      {publishedAt ? formatDateTime(publishedAt) : 'Not published'}
                    </p>
                  </div>
                )}
              </div>

              {/* Published Date */}
              {publishedAt && (
                <div className="flex items-center gap-3 text-white/70">
                  <div className="w-2 h-2 bg-white/50 rounded-full"></div>
                  <p className="text-sm">
                    Added {formatDateTime(publishedAt)}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div >
    )
  }
