import type { Block } from 'payload'

export const Table: Block = {
  slug: 'table',
  interfaceName: 'TableBlock',
  labels: {
    singular: 'Table',
    plural: 'Tables',
  },
  fields: [
    {
      name: 'heading',
      type: 'text',
      label: 'Table Heading',
      localized: true,
      admin: {
        description: 'Optional heading displayed above the table',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Table Description',
      localized: true,
      admin: {
        description: 'Optional description text displayed below the heading',
      },
    },
    {
      name: 'enableResponsive',
      type: 'checkbox',
      label: 'Enable Responsive Mode',
      localized: true,
      defaultValue: true,
      admin: {
        description: 'Makes the table horizontally scrollable on small screens',
      },
    },
    {
      name: 'bordered',
      type: 'checkbox',
      label: 'Show Borders',
      localized: true,
      defaultValue: true,
      admin: {
        description: 'Add borders to table cells',
      },
    },
    {
      name: 'striped',
      type: 'checkbox',
      label: 'Striped Rows',
      localized: true,
      defaultValue: false,
      admin: {
        description: 'Alternate row background colors for better readability',
      },
    },
    {
      name: 'hoverable',
      type: 'checkbox',
      label: 'Hoverable Rows',
      localized: true,
      defaultValue: true,
      admin: {
        description: 'Highlight rows on hover',
      },
    },
    {
      name: 'compact',
      type: 'checkbox',
      label: 'Compact Mode',
      localized: true,
      defaultValue: false,
      admin: {
        description: 'Reduce padding in table cells for a more compact look',
      },
    },
    {
      name: 'textAlignment',
      type: 'select',
      label: 'Text Alignment',
      localized: true,
      defaultValue: 'left',
      options: [
        { label: 'Left', value: 'left' },
        { label: 'Center', value: 'center' },
        { label: 'Right', value: 'right' },
      ],
      admin: {
        description: 'Horizontal alignment of text in cells',
      },
    },
    {
      name: 'headerBackground',
      type: 'select',
      label: 'Header Background',
      localized: true,
      defaultValue: 'default',
      options: [
        { label: 'Default', value: 'default' },
        { label: 'Primary', value: 'primary' },
        { label: 'Secondary', value: 'secondary' },
        { label: 'Accent', value: 'accent' },
        { label: 'Muted', value: 'muted' },
      ],
      admin: {
        description: 'Background color for the table header',
      },
    },
    {
      name: 'headers',
      type: 'array',
      label: 'Table Headers',
      localized: true,
      minRows: 1,
      admin: {
        description: 'Define the table column headers',
      },
      fields: [
        {
          name: 'header',
          type: 'text',
          required: true,
          label: 'Header Text',
          localized: true,
        },
        {
          name: 'width',
          type: 'select',
          label: 'Column Width',
          localized: true,
          defaultValue: 'auto',
          options: [
            { label: 'Auto', value: 'auto' },
            { label: 'Narrow', value: 'narrow' },
            { label: 'Medium', value: 'medium' },
            { label: 'Wide', value: 'wide' },
          ],
        },
      ],
    },
    {
      name: 'rows',
      type: 'array',
      label: 'Table Rows',
      admin: {
        description: 'Add rows to your table',
      },
      fields: [
        {
          name: 'cells',
          type: 'array',
          label: 'Row Cells',
          admin: {
            description: 'Add cells to this row',
          },
          fields: [
            {
              name: 'content',
              type: 'text',
              label: 'Cell Content',
            },
            {
              name: 'highlight',
              type: 'checkbox',
              label: 'Highlight Cell',
              defaultValue: false,
            },
            {
              name: 'bold',
              type: 'checkbox',
              label: 'Bold Text',
              defaultValue: false,
            },
          ],
        },
      ],
    },
    {
      name: 'caption',
      type: 'text',
      label: 'Table Caption',
      admin: {
        description: 'Optional caption displayed below the table (good for accessibility)',
      },
    },
  ],
}
