import { HeaderClient } from './Component.client'
import React from 'react'
import { getGlobal } from '@/utilities/getGlobals'
import type { Config } from 'src/payload-types'

import type { Header } from '@/payload-types'

export async function Header({ locale }: { locale: Config['locale'] }) {
  const headerData: Header = await getGlobal('header', 1, locale)

  return <HeaderClient data={headerData} locale={locale} />
}
