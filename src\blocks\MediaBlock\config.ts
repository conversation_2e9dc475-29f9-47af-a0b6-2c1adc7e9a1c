import { Block } from 'payload'

export const MediaBlock: Block = {
  slug: 'mediaBlock',
  interfaceName: 'MediaBlock',
  labels: {
    singular: 'Media Block',
    plural: 'Media Blocks',
  },
  fields: [
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Content',
          description: 'Media content and gallery settings',
          fields: [
            {
              name: 'galleryEnabled',
              type: 'checkbox',
              label: 'Enable Gallery Mode',
              localized: true,
              defaultValue: false,
              admin: {
                description: 'Switch between single media and gallery mode',
              },
            },
            {
              name: 'media',
              type: 'upload',
              relationTo: 'media',
              localized: true,
              required: true,
              admin: {
                description: 'Image or video to display in this block',
                condition: (_: unknown, siblingData: { galleryEnabled?: boolean }) =>
                  siblingData?.galleryEnabled !== true,
              },
            },
            {
              name: 'multipleMedia',
              type: 'array',
              label: 'Gallery Media Items',
              localized: true,
              minRows: 2,
              maxRows: 12,
              labels: {
                singular: 'Media Item',
                plural: 'Media Items',
              },
              fields: [
                {
                  name: 'media',
                  type: 'upload',
                  relationTo: 'media',
                  localized: true,
                  required: true,
                },
                {
                  name: 'caption',
                  type: 'richText',
                  label: 'Item Caption',
                  localized: true,
                },
              ],
              admin: {
                description: 'Add multiple media items for gallery display',
                condition: (_: unknown, siblingData: { galleryEnabled?: boolean }) =>
                  siblingData?.galleryEnabled === true,
                className: 'gallery-media-items',
              },
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'galleryType',
                  type: 'radio',
                  label: 'Gallery Type',
                  localized: true,
                  options: [
                    { label: 'Grid Layout', value: 'grid' },
                    { label: 'Carousel', value: 'carousel' },
                  ],
                  defaultValue: 'grid',
                  admin: {
                    width: '50%',
                    description: 'Layout style for displaying multiple media items',
                    condition: (_: unknown, siblingData: { galleryEnabled?: boolean }) =>
                      siblingData?.galleryEnabled === true,
                  },
                },
                {
                  name: 'galleryColumns',
                  type: 'select',
                  label: 'Grid Columns',
                  localized: true,
                  options: [
                    { label: '2 Columns', value: '2' },
                    { label: '3 Columns', value: '3' },
                    { label: '4 Columns', value: '4' },
                    { label: '5 Columns', value: '5' },
                    { label: '6 Columns', value: '6' },
                  ],
                  defaultValue: '3',
                  admin: {
                    width: '50%',
                    description: 'Number of columns for grid layout',
                    condition: (
                      _: unknown,
                      siblingData: { galleryEnabled?: boolean; galleryType?: string }
                    ) =>
                      siblingData?.galleryEnabled === true &&
                      siblingData?.galleryType === 'grid',
                  },
                },
              ],
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'gallerySpacing',
                  type: 'select',
                  label: 'Gallery Spacing',
                  localized: true,
                  options: [
                    { label: 'None', value: 'none' },
                    { label: 'X-Small', value: 'xsmall' },
                    { label: 'Small', value: 'small' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Large', value: 'large' },
                    { label: 'X-Large', value: 'xlarge' },
                  ],
                  defaultValue: 'medium',
                  admin: {
                    width: '50%',
                    description: 'Space between gallery items',
                    condition: (_: unknown, siblingData: { galleryEnabled?: boolean }) =>
                      siblingData?.galleryEnabled === true,
                  },
                },
                {
                  name: 'galleryCaptionsEnabled',
                  type: 'checkbox',
                  label: 'Show Individual Captions',
                  localized: true,
                  defaultValue: false,
                  admin: {
                    width: '50%',
                    description: 'Enable individual captions for each gallery item',
                    condition: (_: unknown, siblingData: { galleryEnabled?: boolean }) =>
                      siblingData?.galleryEnabled === true,
                  },
                },
              ],
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'gridLayout',
                  type: 'select',
                  label: 'Grid Layout Type',
                  options: [
                    { label: 'Standard (Equal Height)', value: 'standard' },
                    { label: 'Masonry', value: 'masonry' },
                  ],
                  defaultValue: 'standard',
                  admin: {
                    width: '50%',
                    description: 'Layout style for grid display',
                    condition: (_: unknown, siblingData: { galleryEnabled?: boolean; galleryType?: string }) =>
                      siblingData?.galleryEnabled === true && siblingData?.galleryType === 'grid',
                  },
                },
                {
                  name: 'gridResponsive',
                  type: 'checkbox',
                  label: 'Responsive Columns',
                  defaultValue: true,
                  admin: {
                    width: '50%',
                    description: 'Automatically adjust columns on smaller screens',
                    condition: (_: unknown, siblingData: { galleryEnabled?: boolean; galleryType?: string }) =>
                      siblingData?.galleryEnabled === true && siblingData?.galleryType === 'grid',
                  },
                },
              ],
            },
            {
              name: 'carouselOptions',
              type: 'group',
              label: 'Carousel Options',
              admin: {
                description: 'Additional options for carousel display',
                condition: (_: unknown, siblingData: { galleryEnabled?: boolean; galleryType?: string }) =>
                  siblingData?.galleryEnabled === true && siblingData?.galleryType === 'carousel',
              },
              fields: [
                {
                  type: 'row',
                  fields: [
                    {
                      name: 'autoplay',
                      type: 'checkbox',
                      label: 'Auto-play Slides',
                      defaultValue: true,
                      admin: {
                        width: '50%',
                      },
                    },
                    {
                      name: 'slideDuration',
                      type: 'select',
                      label: 'Slide Duration',
                      options: [
                        { label: 'Quick (3s)', value: '3000' },
                        { label: 'Standard (5s)', value: '5000' },
                        { label: 'Slow (8s)', value: '8000' },
                      ],
                      defaultValue: '5000',
                      admin: {
                        width: '50%',
                        condition: (_: unknown, siblingData: { autoplay?: boolean }) =>
                          siblingData?.autoplay === true,
                      },
                    },
                  ],
                },
                {
                  type: 'row',
                  fields: [
                    {
                      name: 'navStyle',
                      type: 'select',
                      label: 'Navigation Style',
                      options: [
                        { label: 'Arrows Only', value: 'arrows' },
                        { label: 'Dots Only', value: 'dots' },
                        { label: 'Arrows & Dots', value: 'both' },
                        { label: 'None', value: 'none' },
                      ],
                      defaultValue: 'both',
                      admin: {
                        width: '50%',
                      },
                    },
                    {
                      name: 'dotsPosition',
                      type: 'select',
                      label: 'Dots Position',
                      options: [
                        { label: 'Bottom Center', value: 'bottom-center' },
                        { label: 'Bottom Left', value: 'bottom-left' },
                        { label: 'Bottom Right', value: 'bottom-right' },
                        { label: 'Outside Bottom', value: 'outside-bottom' },
                      ],
                      defaultValue: 'bottom-center',
                      admin: {
                        width: '50%',
                        condition: (_: unknown, siblingData: { navStyle?: string }) =>
                          siblingData?.navStyle === 'dots' || siblingData?.navStyle === 'both',
                      },
                    },
                  ],
                },
                {
                  name: 'slideEffect',
                  type: 'select',
                  label: 'Slide Transition Effect',
                  options: [
                    { label: 'Slide', value: 'slide' },
                    { label: 'Fade', value: 'fade' },
                    { label: 'Zoom', value: 'zoom' },
                  ],
                  defaultValue: 'slide',
                },
                {
                  name: 'infiniteLoop',
                  type: 'checkbox',
                  label: 'Infinite Loop',
                  defaultValue: false,
                  admin: {
                    description: 'Allow carousel to loop continuously when reaching the first or last slide',
                    condition: (_: unknown, siblingData: { navStyle?: string }) =>
                      siblingData?.navStyle !== 'none',
                  },
                },
              ],
            },
            {
              name: 'caption',
              type: 'richText',
              label: 'Caption',
              admin: {
                description: 'Optional caption below the media',
                condition: (data) => !data?.galleryEnabled,
              },
            },
          ],
        },
        {
          label: 'Layout & Styling',
          description: 'Control the appearance and layout',
          fields: [
            {
              type: 'row',
              fields: [
                {
                  name: 'size',
                  type: 'select',
                  label: 'Media Size',
                  options: [
                    { label: 'Small', value: 'small' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Large', value: 'large' },
                    { label: 'Full Width', value: 'full' },
                  ],
                  defaultValue: 'medium',
                  admin: {
                    width: '50%',
                    description: 'Width of the media in the container',
                  },
                },
                {
                  name: 'aspectRatio',
                  type: 'select',
                  label: 'Aspect Ratio',
                  options: [
                    { label: 'Automatic (Original)', value: 'auto' },
                    { label: 'Square (1:1)', value: 'square' },
                    { label: 'Video (16:9)', value: 'video' },
                    { label: 'Portrait (3:4)', value: 'portrait' },
                    { label: 'Ultra-wide (21:9)', value: 'ultrawide' },
                  ],
                  defaultValue: 'auto',
                  admin: {
                    width: '50%',
                    description: 'Aspect ratio of the media',
                  },
                },
              ],
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'alignment',
                  type: 'select',
                  label: 'Alignment',
                  options: [
                    { label: 'Left', value: 'left' },
                    { label: 'Center', value: 'center' },
                    { label: 'Right', value: 'right' },
                  ],
                  defaultValue: 'center',
                  admin: {
                    width: '50%',
                    description: 'Horizontal alignment within the container',
                  },
                },
                {
                  name: 'backgroundColor',
                  type: 'select',
                  label: 'Background Color',
                  options: [
                    { label: 'Background (Default)', value: 'background' },
                    { label: 'Primary', value: 'primary' },
                    { label: 'Secondary', value: 'secondary' },
                    { label: 'Accent', value: 'accent' },
                    { label: 'Muted', value: 'muted' },
                    { label: 'Card', value: 'card' },
                  ],
                  defaultValue: 'background',
                  admin: {
                    width: '50%',
                    description: 'Theme-aware background color for the media block',
                  },
                },
              ],
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'paddingTop',
                  type: 'select',
                  label: 'Top Spacing',
                  options: [
                    { label: 'None', value: 'none' },
                    { label: 'Small', value: 'small' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Large', value: 'large' },
                  ],
                  defaultValue: 'medium',
                  admin: {
                    width: '50%',
                    description: 'Amount of space above the media',
                  },
                },
                {
                  name: 'paddingBottom',
                  type: 'select',
                  label: 'Bottom Spacing',
                  options: [
                    { label: 'None', value: 'none' },
                    { label: 'Small', value: 'small' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Large', value: 'large' },
                  ],
                  defaultValue: 'medium',
                  admin: {
                    width: '50%',
                    description: 'Amount of space below the media',
                  },
                },
              ],
            },
          ],
        },
        {
          label: 'Enhancement',
          description: 'Additional visual enhancements',
          fields: [
            {
              name: 'captionPosition',
              type: 'select',
              label: 'Caption Position',
              options: [
                { label: 'Below Media', value: 'below' },
                { label: 'Overlay (Top)', value: 'overlay-top' },
                { label: 'Overlay (Bottom)', value: 'overlay-bottom' },
              ],
              defaultValue: 'below',
              admin: {
                description: 'Where to position the caption relative to the media',
              },
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'captionStyle',
                  type: 'select',
                  label: 'Caption Style',
                  options: [
                    { label: 'Default', value: 'default' },
                    { label: 'Accent', value: 'accent' },
                    { label: 'Muted', value: 'muted' },
                    { label: 'Large', value: 'large' },
                    { label: 'Small', value: 'small' },
                  ],
                  defaultValue: 'default',
                  admin: {
                    width: '50%',
                    description: 'Visual style for the caption',
                  },
                },
                {
                  name: 'captionAlignment',
                  type: 'select',
                  label: 'Caption Alignment',
                  options: [
                    { label: 'Left', value: 'left' },
                    { label: 'Center', value: 'center' },
                    { label: 'Right', value: 'right' },
                  ],
                  defaultValue: 'left',
                  admin: {
                    width: '50%',
                    description: 'Horizontal alignment of the caption text',
                  },
                },
              ],
            },
            {
              name: 'hoverEffect',
              type: 'select',
              label: 'Hover Effect',
              options: [
                { label: 'None', value: 'none' },
                { label: 'Zoom', value: 'zoom' },
                { label: 'Lift', value: 'lift' },
                { label: 'Glow', value: 'glow' },
              ],
              defaultValue: 'none',
              admin: {
                description: 'Effect applied when hovering over the media',
              },
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'borderRadius',
                  type: 'checkbox',
                  label: 'Rounded Corners',
                  defaultValue: true,
                  admin: {
                    width: '50%',
                    description: 'Apply rounded corners to the media',
                  },
                },
                {
                  name: 'shadow',
                  type: 'checkbox',
                  label: 'Apply Shadow',
                  defaultValue: false,
                  admin: {
                    width: '50%',
                    description: 'Add a subtle shadow effect to the media',
                  },
                },
              ],
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'backgroundTexture',
                  type: 'select',
                  label: 'Background Texture',
                  options: [
                    { label: 'None', value: 'none' },
                    { label: 'Dots', value: 'dots' },
                    { label: 'Grid', value: 'grid' },
                    { label: 'Lines', value: 'lines' },
                    { label: 'Waves', value: 'waves' },
                    { label: 'Noise', value: 'noise' },
                  ],
                  defaultValue: 'none',
                  admin: {
                    width: '50%',
                    description: 'Add a texture pattern to the background',
                    condition: (data, siblingData) => siblingData.backgroundColor !== 'background',
                  },
                },
                {
                  name: 'textureTarget',
                  type: 'radio',
                  label: 'Texture Target',
                  options: [
                    { label: 'Container', value: 'container' },
                    { label: 'Block', value: 'block' },
                  ],
                  defaultValue: 'container',
                  admin: {
                    width: '50%',
                    description: 'Where to apply the texture',
                    condition: (data, siblingData) => siblingData.backgroundTexture !== 'none',
                  },
                },
              ],
            },
            {
              name: 'textureOpacity',
              type: 'select',
              label: 'Texture Opacity',
              options: [
                { label: 'Very Light (10%)', value: 'very-light' },
                { label: 'Light (20%)', value: 'light' },
                { label: 'Medium (30%)', value: 'medium' },
                { label: 'Strong (50%)', value: 'strong' },
                { label: 'Very Strong (70%)', value: 'very-strong' },
              ],
              defaultValue: 'medium',
              admin: {
                description: 'Control the opacity of the texture pattern',
                condition: (data, siblingData) => siblingData.backgroundTexture !== 'none',
              },
            },
          ],
        },
      ],
    },
  ],
}
