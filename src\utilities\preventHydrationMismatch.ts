/**
 * Utility functions to prevent hydration mismatches
 */

/**
 * Safe way to access localStorage that won't cause hydration mismatches
 * @param key - localStorage key
 * @param defaultValue - default value if key doesn't exist or localStorage is unavailable
 * @returns the stored value or default value
 */
export const safeLocalStorageGet = (key: string, defaultValue: string = ''): string => {
  if (typeof window === 'undefined') {
    return defaultValue
  }
  
  try {
    return localStorage.getItem(key) || defaultValue
  } catch (error) {
    console.warn(`Failed to access localStorage for key "${key}":`, error)
    return defaultValue
  }
}

/**
 * Safe way to set localStorage that won't cause hydration mismatches
 * @param key - localStorage key
 * @param value - value to store
 */
export const safeLocalStorageSet = (key: string, value: string): void => {
  if (typeof window === 'undefined') {
    return
  }
  
  try {
    localStorage.setItem(key, value)
  } catch (error) {
    console.warn(`Failed to set localStorage for key "${key}":`, error)
  }
}

/**
 * Safe way to access sessionStorage that won't cause hydration mismatches
 * @param key - sessionStorage key
 * @param defaultValue - default value if key doesn't exist or sessionStorage is unavailable
 * @returns the stored value or default value
 */
export const safeSessionStorageGet = (key: string, defaultValue: string = ''): string => {
  if (typeof window === 'undefined') {
    return defaultValue
  }
  
  try {
    return sessionStorage.getItem(key) || defaultValue
  } catch (error) {
    console.warn(`Failed to access sessionStorage for key "${key}":`, error)
    return defaultValue
  }
}

/**
 * Safe way to set sessionStorage that won't cause hydration mismatches
 * @param key - sessionStorage key
 * @param value - value to store
 */
export const safeSessionStorageSet = (key: string, value: string): void => {
  if (typeof window === 'undefined') {
    return
  }
  
  try {
    sessionStorage.setItem(key, value)
  } catch (error) {
    console.warn(`Failed to set sessionStorage for key "${key}":`, error)
  }
}

/**
 * Safe way to format dates that prevents hydration mismatches
 * Uses a consistent format that works on both server and client
 * @param dateString - ISO date string
 * @param locale - locale for formatting (defaults to 'en-US')
 * @returns formatted date string
 */
export const safeFormatDate = (dateString: string, locale: string = 'en-US'): string => {
  try {
    const date = new Date(dateString)
    
    // Use a simple, consistent format that works on both server and client
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    
    return `${month}/${day}/${year}`
  } catch (error) {
    console.warn('Failed to format date:', error)
    return 'Invalid Date'
  }
}

/**
 * Hook to safely check if we're on the client side
 * Prevents hydration mismatches when using browser-only APIs
 */
export const useClientSide = (): boolean => {
  return typeof window !== 'undefined'
}

/**
 * Safe way to get current timestamp that prevents hydration mismatches
 * Returns null on server, actual timestamp on client
 */
export const safeGetTimestamp = (): number | null => {
  if (typeof window === 'undefined') {
    return null
  }
  return Date.now()
}

/**
 * Safe way to generate random values that prevents hydration mismatches
 * Returns a consistent value on server, random on client
 */
export const safeRandom = (seed: number = 0): number => {
  if (typeof window === 'undefined') {
    // Return a consistent value on server based on seed
    return seed
  }
  return Math.random()
}
