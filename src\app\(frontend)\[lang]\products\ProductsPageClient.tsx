'use client'

import React, { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { ProductFilters } from '@/components/ProductFilters'
import { CollectionArchive } from '@/components/CollectionArchive'
import { CardProductData } from '@/components/Card'
import { useProductFilters } from '@/hooks/useProductFilters'
import { ProductCategory, getFormatOptionsForCategory } from '@/types/filters'

interface ProductsPageClientProps {
  initialProducts: CardProductData[]
  categories: ProductCategory[]
  locale: string
  totalProducts: number
}

export const ProductsPageClient: React.FC<ProductsPageClientProps> = ({
  initialProducts,
  categories,
  locale,
  totalProducts
}) => {
  const [filteredCount, setFilteredCount] = useState(totalProducts)

  // Use the custom filter hook for state management
  const { filters, actions, isLoading, hasActiveFilters } = useProductFilters({
    syncWithURL: true,
    showLoadingState: true,
    loadingDelay: 300,
    scrollToTop: false,
    onFiltersChange: (newFilters) => {
      // Optional: Add analytics tracking here
      console.log('Filters changed:', newFilters)
    }
  })

  // Get format options based on selected category
  const formatOptions = getFormatOptionsForCategory(filters.category, locale)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center max-w-3xl mx-auto">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6"
            >
              {locale === 'fr' ? 'Nos Produits' : 'Our Products'}
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="text-xl text-gray-600 mb-8"
            >
              {locale === 'fr'
                ? 'Découvrez notre collection de meubles et produits pour la maison'
                : 'Discover our collection of furniture and home products'
              }
            </motion.p>

            {/* Results Counter */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-flex items-center gap-2 px-4 py-2 bg-red-50 text-red-700 rounded-full text-sm font-medium"
            >
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <span>
                {filteredCount} {locale === 'fr' ? 'produits' : 'products'}
                {hasActiveFilters && (
                  <span className="text-red-600">
                    {' '}{locale === 'fr' ? 'trouvés' : 'found'}
                  </span>
                )}
              </span>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">

          {/* Filters Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="lg:col-span-1"
          >
            <div className="sticky top-8">
              <ProductFilters
                categories={categories}
                selectedCategory={filters.category}
                selectedFormat={filters.format}
                onCategoryChange={actions.setCategory}
                onFormatChange={actions.setFormat}
                onClearFilters={actions.clearFilters}
                locale={locale}
                isLoading={isLoading}
              />
            </div>
          </motion.div>

          {/* Products Grid */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-3"
          >
            <CollectionArchive
              posts={initialProducts}
              relationTo="products"
              locale={locale}
              selectedCategory={filters.category}
              selectedFormat={filters.format}
              onFilteredCountChange={setFilteredCount}
              showFilters={true}
            />
          </motion.div>
        </div>
      </div>
    </div>
  )
}
