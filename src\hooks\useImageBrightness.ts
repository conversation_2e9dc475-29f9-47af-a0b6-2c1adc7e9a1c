'use client';

import { useState, useEffect } from 'react';
import { analyzeImageBrightness } from '@/utilities/imageAnalysis';
import { useIsClient } from '@/hooks/useIsClient';
import { safeSessionStorageGet, safeSessionStorageSet } from '@/utilities/preventHydrationMismatch';
import type { Theme } from '@/providers/Theme/types';

interface UseImageBrightnessOptions {
  defaultTheme?: Theme;
  enableAdaptiveTextColor?: boolean;
  fixedTextTheme?: Theme;
}

/**
 * Hook to analyze image brightness and determine if text should be light or dark
 * @param imageUrl The URL of the image to analyze
 * @param options Options for the hook
 * @returns Object with textTheme ('dark' or 'light') and isLoading state
 */
export const useImageBrightness = (imageUrl?: string, options: UseImageBrightnessOptions = {}) => {
  const {
    defaultTheme = 'dark',
    enableAdaptiveTextColor = true,
    fixedTextTheme = 'light'
  } = options;

  // Initialize with fixed theme to prevent hydration mismatch
  const [textTheme, setTextTheme] = useState<Theme>(fixedTextTheme);
  const [isLoading, setIsLoading] = useState(false);
  const isClient = useIsClient();

  // Set initial state after hydration
  useEffect(() => {
    if (!isClient) return;

    setIsLoading(enableAdaptiveTextColor);
    if (!enableAdaptiveTextColor) {
      setTextTheme(fixedTextTheme);
    } else {
      setTextTheme(defaultTheme);
    }
  }, [isClient, enableAdaptiveTextColor, fixedTextTheme, defaultTheme]);

  useEffect(() => {
    // Only run on client side after hydration
    if (!isClient) return;

    // If adaptive text color is disabled, use the fixed theme
    if (!enableAdaptiveTextColor) {
      setTextTheme(fixedTextTheme);
      setIsLoading(false);
      return;
    }

    // Reset when image URL changes
    setIsLoading(true);

    // Default to the default theme if no image
    if (!imageUrl) {
      setTextTheme(defaultTheme);
      setIsLoading(false);
      return;
    }

    let isMounted = true;

    // Check if we have a cached result for this image (only on client)
    const cachedResult = safeSessionStorageGet(`imgBrightness-${imageUrl}`);

    if (cachedResult) {
      setTextTheme(cachedResult as Theme);
      setIsLoading(false);
      return;
    }

    // Analyze the image brightness
    analyzeImageBrightness(imageUrl)
      .then((theme) => {
        if (isMounted) {
          setTextTheme(theme);
          // Cache the result (only on client)
          safeSessionStorageSet(`imgBrightness-${imageUrl}`, theme);
        }
      })
      .catch((error) => {
        console.error('Image brightness analysis failed:', error);
        if (isMounted) {
          setTextTheme(defaultTheme); // Fallback to default
        }
      })
      .finally(() => {
        if (isMounted) {
          setIsLoading(false);
        }
      });

    return () => {
      isMounted = false;
    };
  }, [imageUrl, defaultTheme, enableAdaptiveTextColor, fixedTextTheme, isClient]);

  return {
    textTheme,
    isLoading
  };
};

export default useImageBrightness;
