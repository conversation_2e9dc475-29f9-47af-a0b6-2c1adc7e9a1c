import { Button, type ButtonProps } from '@/components/ui/button'
import { cn } from '@/utilities/ui'
import Link from 'next/link'
import React from 'react'
import type { Config } from 'src/payload-types'
import type { Page, Post } from '@/payload-types'

export type CMSLinkType = {
  appearance?: 'inline' | ButtonProps['variant']
  children?: React.ReactNode
  className?: string
  label?: string | null
  newTab?: boolean | null
  reference?: {
    relationTo: 'pages' | 'posts'
    value: Page | Post | string | number
  } | null
  size?: ButtonProps['size'] | null
  type?: 'custom' | 'reference' | null
  url?: string | null
  locale: Config['locale']
}

export const CMSLink: React.FC<CMSLinkType> = (props) => {
  const {
    type,
    appearance = 'inline',
    children,
    className,
    label,
    newTab,
    reference,
    size: sizeFromProps,
    url,
    locale
  } = props

  // Generate href with proper error handling
  const href = React.useMemo(() => {
    try {
      if (type === 'reference' && reference?.value) {
        // Handle case where reference.value is a populated object with slug
        if (typeof reference.value === 'object' && reference.value.slug) {
          const basePath = reference.relationTo !== 'pages' ? `/${reference.relationTo}` : ''
          return `/${locale}${basePath}/${reference.value.slug}`
        }

        // Handle case where reference.value is just an ID string
        // In this case, we can't generate a proper URL without the slug
        // This usually happens when the reference is not properly populated
        if (typeof reference.value === 'string') {
          console.warn('CMSLink: Reference value is not populated, received ID:', reference.value)
          return null
        }
      }

      // Fall back to custom URL with locale prefix
      if (url) {
        // If URL already starts with a locale, return as is
        if (url.startsWith('/en/') || url.startsWith('/fr/')) {
          return url
        }
        // If URL is external (starts with http), return as is
        if (url.startsWith('http')) {
          return url
        }
        // Add locale prefix to internal URLs
        return `/${locale}${url.startsWith('/') ? url : `/${url}`}`
      }
      return null
    } catch (error) {
      console.error('CMSLink: Error generating href:', error)
      return null
    }
  }, [type, reference, url, locale])

  // Don't render if no valid href
  if (!href) {
    // Only show warning in development
    if (process.env.NODE_ENV === 'development') {
      console.warn('CMSLink: No valid href generated', { type, reference, url, label })
    }
    return null
  }

  const size = appearance === 'link' ? 'clear' : sizeFromProps
  const newTabProps = newTab ? { rel: 'noopener noreferrer', target: '_blank' } : {}

  /* Ensure we don't break any styles set by richText */
  if (appearance === 'inline') {
    return (
      <Link className={cn(className)} href={href || url || ''} {...newTabProps} >
        {label && label}
        {children && children}
      </Link>
    )
  }

  return (
    <Button asChild className={className} size={size} variant={appearance}>
      <Link className={cn(className)} href={href || url || ''} {...newTabProps} >
        {label && label}
        {children && children}
      </Link>
    </Button>
  )
}
