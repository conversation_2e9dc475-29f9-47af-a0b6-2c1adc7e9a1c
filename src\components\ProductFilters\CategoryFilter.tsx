'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, Check } from 'lucide-react'
import { cn } from '@/utilities/ui'

export interface ProductCategory {
  id: string
  title: string
  slug: string
}

interface CategoryFilterProps {
  categories: ProductCategory[]
  selectedCategory: string | null
  onCategoryChange: (categorySlug: string | null) => void
  className?: string
  locale?: string
}

export const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedCategory,
  onCategoryChange,
  className,
  locale = 'en'
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // Filter categories based on search term
  const filteredCategories = categories.filter(category =>
    category.title.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Get selected category display name
  const selectedCategoryName = selectedCategory 
    ? categories.find(cat => cat.slug === selectedCategory)?.title || 'Unknown Category'
    : locale === 'fr' ? 'Tous les produits' : 'All Products'

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-category-filter]')) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  return (
    <div className={cn('relative', className)} data-category-filter>
      {/* Filter Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'flex items-center justify-between w-full px-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm hover:border-red-300 hover:shadow-md transition-all duration-200',
          isOpen && 'border-red-500 shadow-md ring-2 ring-red-500/20'
        )}
      >
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-red-500 rounded-full"></div>
          <span className="font-medium text-gray-900">
            {locale === 'fr' ? 'Catégorie:' : 'Category:'} {selectedCategoryName}
          </span>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown className="w-5 h-5 text-gray-500" />
        </motion.div>
      </button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 overflow-hidden"
          >
            {/* Search Input */}
            <div className="p-3 border-b border-gray-100">
              <input
                type="text"
                placeholder={locale === 'fr' ? 'Rechercher une catégorie...' : 'Search categories...'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-3 py-2 text-sm border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500/20 focus:border-red-500"
              />
            </div>

            {/* Options List */}
            <div className="max-h-64 overflow-y-auto">
              {/* All Products Option */}
              <button
                onClick={() => {
                  onCategoryChange(null)
                  setIsOpen(false)
                  setSearchTerm('')
                }}
                className={cn(
                  'flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150',
                  !selectedCategory && 'bg-red-50 text-red-700'
                )}
              >
                <span className="font-medium">
                  {locale === 'fr' ? 'Tous les produits' : 'All Products'}
                </span>
                {!selectedCategory && (
                  <Check className="w-4 h-4 text-red-600" />
                )}
              </button>

              {/* Category Options */}
              {filteredCategories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => {
                    onCategoryChange(category.slug)
                    setIsOpen(false)
                    setSearchTerm('')
                  }}
                  className={cn(
                    'flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150',
                    selectedCategory === category.slug && 'bg-red-50 text-red-700'
                  )}
                >
                  <span className="font-medium">{category.title}</span>
                  {selectedCategory === category.slug && (
                    <Check className="w-4 h-4 text-red-600" />
                  )}
                </button>
              ))}

              {/* No Results */}
              {filteredCategories.length === 0 && searchTerm && (
                <div className="px-4 py-6 text-center text-gray-500">
                  <p className="text-sm">
                    {locale === 'fr' ? 'Aucune catégorie trouvée' : 'No categories found'}
                  </p>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
