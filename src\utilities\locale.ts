import { locales, defaultLocale, type Locale } from '../../middleware'

/**
 * Check if a string is a valid locale
 */
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale)
}

/**
 * Get locale from pathname
 */
export function getLocaleFromPathname(pathname: string): Locale | null {
  const segments = pathname.split('/')
  const potentialLocale = segments[1]

  if (potentialLocale && isValidLocale(potentialLocale)) {
    return potentialLocale
  }

  return null
}

/**
 * Remove locale from pathname
 */
export function removeLocaleFromPathname(pathname: string): string {
  const locale = getLocaleFromPathname(pathname)
  if (locale) {
    return pathname.replace(`/${locale}`, '') || '/'
  }
  return pathname
}

/**
 * Add locale to pathname
 */
export function addLocaleToPathname(pathname: string, locale: Locale): string {
  const cleanPathname = removeLocaleFromPathname(pathname)
  return `/${locale}${cleanPathname === '/' ? '' : cleanPathname}`
}

/**
 * Switch locale in current pathname
 */
export function switchLocaleInPathname(pathname: string, newLocale: Locale): string {
  const cleanPathname = removeLocaleFromPathname(pathname)
  return addLocaleToPathname(cleanPathname, newLocale)
}

/**
 * Get current locale from pathname or return default
 */
export function getCurrentLocale(pathname: string): Locale {
  return getLocaleFromPathname(pathname) || defaultLocale
}

/**
 * Get locale display name
 */
export function getLocaleDisplayName(locale: Locale): string {
  const displayNames: Record<Locale, string> = {
    en: 'English',
    fr: 'Français',
  }
  return displayNames[locale] || locale
}

/**
 * Get locale flag emoji
 */
export function getLocaleFlag(locale: Locale): string {
  const flags: Record<Locale, string> = {
    en: '🇺🇸',
    fr: '🇫🇷',
  }
  return flags[locale] || '🌐'
}

/**
 * Get all available locales with display information
 */
export function getAvailableLocales() {
  return locales.map(locale => ({
    code: locale,
    label: getLocaleDisplayName(locale),
    flag: getLocaleFlag(locale),
  }))
}

/**
 * Check if current pathname is the home page for a locale
 */
export function isHomePage(pathname: string): boolean {
  const locale = getLocaleFromPathname(pathname)
  if (locale) {
    return pathname === `/${locale}` || pathname === `/${locale}/`
  }
  return pathname === '/' || pathname === ''
}

/**
 * Generate localized URL for a given path
 */
export function getLocalizedUrl(path: string, locale: Locale, baseUrl?: string): string {
  const cleanPath = path.startsWith('/') ? path : `/${path}`
  const localizedPath = addLocaleToPathname(cleanPath, locale)

  if (baseUrl) {
    return new URL(localizedPath, baseUrl).toString()
  }

  return localizedPath
}

/**
 * Get alternate URLs for SEO (hreflang)
 */
export function getAlternateUrls(pathname: string, baseUrl: string): Array<{ locale: Locale; url: string }> {
  const cleanPathname = removeLocaleFromPathname(pathname)

  return locales.map(locale => ({
    locale,
    url: getLocalizedUrl(cleanPathname, locale, baseUrl),
  }))
}
