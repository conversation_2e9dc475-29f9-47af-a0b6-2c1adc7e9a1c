import { s3Storage } from '@payloadcms/storage-s3'
import { mongooseAdapter } from '@payloadcms/db-mongodb'

import sharp from 'sharp' // sharp-import
import path from 'path'
import { buildConfig, PayloadRequest } from 'payload'
import { fileURLToPath } from 'url'

import { Categories } from './collections/Categories'
import { Media } from './collections/Media'
import { Pages } from './collections/Pages'
import { Posts } from './collections/Posts'
import { Products } from './collections/Products'
import { ProductCategories } from './collections/ProductCategories'
import { Users } from './collections/Users'
import { Header } from './Header/config'
import { Footer } from './Footer/config'
import { Theme } from './Theme/config'
import { plugins } from './plugins'
import { getServerSideURL } from './utilities/getURL'
import { defaultEditorFeatures, lexicalEditor } from '@payloadcms/richtext-lexical'
import { TextColorFeature } from '@nhayhoc/payloadcms-lexical-ext'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  localization: {
    locales: [
      {
        label: 'English',
        code: 'en',
      },
      {
        label: 'French',
        code: 'fr',
      }
    ],
    defaultLocale: 'fr',
    fallback: true,
  },
  admin: {
    components: {
      // The `BeforeLogin` component renders a message that you see while logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below and the import `BeforeLogin` statement on line 15.
      beforeLogin: ['@/components/BeforeLogin'],
      // The `BeforeDashboard` component renders the 'welcome' block that you see after logging into your admin panel.
      // Feel free to delete this at any time. Simply remove the line below and the import `BeforeDashboard` statement on line 15.
      //beforeDashboard: ['@/components/BeforeDashboard'],
      graphics: {
        Logo: '@/assets/AdminLogo.tsx',
      },
    },
    importMap: {
      baseDir: path.resolve(dirname),
    },
    user: Users.slug,
    livePreview: {
      breakpoints: [
        {
          label: 'Mobile',
          name: 'mobile',
          width: 375,
          height: 667,
        },
        {
          label: 'Tablet',
          name: 'tablet',
          width: 768,
          height: 1024,
        },
        {
          label: 'Desktop',
          name: 'desktop',
          width: 1440,
          height: 900,
        },
      ],
    },
  },
  // This config helps us configure global or default features that the other editors can inherit
  editor: lexicalEditor({
    features: [...defaultEditorFeatures, TextColorFeature()],
  }),
  db: mongooseAdapter({
    url: process.env.DATABASE_URI || '',
  }),
  collections: [Pages, Posts, Products, Media, Categories, ProductCategories, Users],
  // The Forms collection and form-submissions collections are automatically created by the formBuilderPlugin
  cors: [getServerSideURL()].filter(Boolean),
  globals: [Header, Footer, Theme],
  plugins: [
    ...plugins,
    // Always include the S3 storage plugin, but enable it only when all required env vars are present
    s3Storage({
      // Configure which collections to use S3 storage
      collections: {
        media: true,
      },
      // Enable S3 storage only when all required env vars are present
      enabled: !!(process.env.NODE_ENV === 'production' &&
        process.env.S3_BUCKET &&
        process.env.S3_ENDPOINT &&
        process.env.S3_ACCESS_KEY_ID &&
        process.env.S3_SECRET_ACCESS_KEY),
      // S3 bucket to use for storage - use dummy value if not provided
      bucket: process.env.S3_BUCKET || 'dummy-bucket',
      // AWS S3 client configuration
      config: {
        endpoint: process.env.S3_ENDPOINT || 'http://localhost:9000',
        region: process.env.S3_REGION || 'us-east-1',
        credentials: {
          accessKeyId: process.env.S3_ACCESS_KEY_ID || 'dummy-key',
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY || 'dummy-secret',
        },
        forcePathStyle: true, // Required for MinIO
      },
    }),
  ],
  secret: process.env.PAYLOAD_SECRET,
  sharp,
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  jobs: {
    access: {
      run: ({ req }: { req: PayloadRequest }): boolean => {
        // Allow logged in users to execute this endpoint (default)
        if (req.user) return true

        // If there is no logged in user, then check
        // for the Vercel Cron secret to be present as an
        // Authorization header:
        const authHeader = req.headers.get('authorization')
        return authHeader === `Bearer ${process.env.CRON_SECRET}`
      },
    },
    tasks: [],
  },
})
