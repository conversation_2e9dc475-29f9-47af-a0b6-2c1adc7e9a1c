import type { Metadata } from 'next'

import { RelatedProducts } from '@/blocks/RelatedProducts/Component'
import { PayloadRedirects } from '@/components/PayloadRedirects'
import { ProductGallery, ProductSpecifications } from '@/components/ProductDetail'
import configPromise from '@payload-config'
import { getPayload } from 'payload'
import { draftMode } from 'next/headers'
import React, { cache } from 'react'
import RichText from '@/components/RichText'

// Temporary Product type until payload types are regenerated
type Product = {
  id: string
  title: string
  slug: string
  heroImage?: any
  categories?: any[]
  populatedAuthors?: any[]
  publishedAt?: string
  price?: number
  currency?: string
  description?: any
  relatedProducts?: any[]
}

import { ProductHero } from '@/heros/ProductHero'
import { generateMeta } from '@/utilities/generateMeta'
import PageClient from './page.client'
import { LivePreviewListener } from '@/components/LivePreviewListener'
import { Media } from '@/components/Media'

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })
  const products = await payload.find({
    collection: 'products',
    draft: false,
    limit: 1000,
    overrideAccess: false,
    pagination: false,
    select: {
      slug: true,
    },
  })

  const params = products.docs.map(({ slug }) => {
    return { slug }
  })

  return params
}

type Args = {
  params: Promise<{
    slug?: string
  }>
}

export default async function Product({ params: paramsPromise }: Args) {
  const { isEnabled: draft } = await draftMode()
  const { slug = '' } = await paramsPromise
  const url = '/products/' + slug
  const product = await queryProductBySlug({ slug })

  if (!product) return <PayloadRedirects url={url} />

  return (
    <article className="pt-16 pb-16">
      <PageClient />

      {/* Allows redirects for valid pages too */}
      <PayloadRedirects disableNotFound url={url} />

      <ProductHero product={product} />

      {/* Product Details Section */}
      <div className="bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="max-w-7xl mx-auto">

            {/* Product Information Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">

              {/* Enhanced Product Gallery */}
              <div className="space-y-6">
                <ProductGallery
                  heroImage={product.heroImage}
                  gallery={product.gallery}
                  productTitle={product.title}
                />
              </div>

              {/* Enhanced Product Specifications */}
              <div className="space-y-8">
                <ProductSpecifications
                  dimensions={product.dimensions}
                  materials={product.materials}
                  colors={product.colors}
                  format={product.format}
                  categories={product.categories}
                  locale={slug.split('/')[0] || 'en'}
                />

                {/* Materials */}
                {product.materials && product.materials.length > 0 && (
                  <div className="bg-green-50 rounded-2xl p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Materials</h3>
                    <div className="space-y-2">
                      {product.materials.map((material, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <span className="font-medium">{material.material}</span>
                          {material.percentage && (
                            <span className="text-sm text-gray-600">{material.percentage}%</span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Colors */}
                {product.colors && product.colors.length > 0 && (
                  <div className="bg-blue-50 rounded-2xl p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Available Colors</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {product.colors.map((color, index) => (
                        <div key={index} className="flex items-center gap-3">
                          {color.hexCode && (
                            <div
                              className="w-6 h-6 rounded-full border-2 border-white shadow-md"
                              style={{ backgroundColor: color.hexCode }}
                            />
                          )}
                          <span className="font-medium">{color.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Product Description */}
            <div className="prose prose-lg max-w-none mb-16">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Product Description</h2>
              <RichText
                className="leading-relaxed text-gray-700"
                data={product.description}
                enableGutter={false}
              />
            </div>

            {/* Related Products */}
            {product.relatedProducts && product.relatedProducts.length > 0 && (
              <div className="border-t border-gray-200 pt-16">
                <RelatedProducts
                  className=""
                  docs={product.relatedProducts.filter((product) => typeof product === 'object')}
                />
              </div>
            )}
          </div>
        </div>
      </div>
      <LivePreviewListener />
    </article>
  )
}

export async function generateMetadata({ params: paramsPromise }: Args): Promise<Metadata> {
  const { slug = '' } = await paramsPromise
  const product = await queryProductBySlug({ slug })

  return generateMeta({ doc: product })
}

const queryProductBySlug = cache(async ({ slug }: { slug: string }) => {
  const { isEnabled: draft } = await draftMode()

  const payload = await getPayload({ config: configPromise })

  const result = await payload.find({
    collection: 'products',
    draft,
    limit: 1,
    overrideAccess: draft,
    pagination: false,
    where: {
      slug: {
        equals: slug,
      },
    },
  })

  return result.docs?.[0] || null
})
