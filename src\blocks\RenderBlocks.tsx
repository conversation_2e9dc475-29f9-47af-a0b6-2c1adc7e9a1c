import React, { Fragment } from 'react'

import type { Page } from '@/payload-types'

import { ArchiveBlock } from '@/blocks/ArchiveBlock/Component'
import { ContentBlock } from '@/blocks/Content/Component'
import { ContentWithImageBlock } from '@/blocks/ContentWithImage/Component'
import { CallToActionBlock } from '@/blocks/CallToAction/Component'
import { FormBlock } from '@/blocks/Form/Component'
import { MediaBlock } from '@/blocks/MediaBlock/Component'
import { FeatureGridBlock } from '@/blocks/FeatureGrid/Component'
import { BackgroundMediaBlock } from '@/blocks/BackgroundMedia/Component'
import { QuoteBlock } from '@/blocks/Quote/Component'
import { TestimonialBlock } from '@/blocks/Testimonial/Component'
import { TextWithImageBlock } from '@/blocks/TextWithImage/Component'
import { MaterialsShowcaseBlock } from '@/blocks/MaterialsShowcase'
import { KaizenDiagramBlock } from '@/blocks/Kaizen/Component'
import { HeroBlock } from '@/blocks/Hero/Component'
import { ValueGridBlock } from '@/blocks/ValueGrid/Component'
import { CertificationsBlock } from '@/blocks/Certifications/Component'
import { CertificationsAlternateBlock } from '@/blocks/CertificationsAlternate/Component'
import { StatsCounterBlock } from '@/blocks/StatsCounter/Component'
import { TeamBioBlock } from '@/blocks/TeamBio/Component'
import { SlidingTextBlock } from '@/blocks/SlidingText'
import { EnhancedContentWithImageBlock } from '@/blocks/EnhancedContentWithImage'
import { TableBlock } from '@/blocks/Table/Component'
import { TimelineBlock } from '@/blocks/Timeline/Component'
import { FAQAccordionBlock } from '@/blocks/FAQAccordion/Component'
import { TabsBlock } from '@/blocks/Tabs/Component'
import { BeforeAfterBlock } from '@/blocks/BeforeAfter/Component'
import { BreadcrumbBlock } from '@/blocks/Breadcrumb/Component'
import { StickyScrollRevealBlock } from '@/blocks/StickyScrollReveal/Component'
import { LayoutGridBlock } from '@/blocks/LayoutGrid/Component'
import { StepperBlock } from '@/blocks/StepperBlock/Component'
import { VisualTimelineBlock } from '@/blocks/VisualTimeline/Component'
import { WorkflowProcessBlock } from '@/blocks/WorkflowProcess/Component'
import { ProcessDiagramBlock } from '@/blocks/ProcessDiagram/Component'
import ApproachFeaturesBlock from '@/blocks/ApproachFeatures/Component'
import { TestimonialsBlockComponent } from '@/blocks/Testimonials'
import { DropCapStoryBlock } from '@/blocks/DropCapStory/Component'
import { PartnersLogosBlock } from '@/blocks/PartnersLogos/Component'
import { CarouselHeroBlock } from '@/blocks/CarouselHero/Component'
import { ProjectsShowcaseBlock } from '@/blocks/ProjectsShowcase/Component'
import { TeamBlock } from '@/blocks/Team/Component'
import { SavoirFaireBlock } from '@/blocks/SavoirFaire/Component'
import { ServicesBlock } from '@/blocks/Services/Component'
import { ProjectStepsBlock } from '@/blocks/ProjectSteps/Component'

const blockComponents = {
  backgroundMedia: BackgroundMediaBlock,
  likeHero: BackgroundMediaBlock, // Map old likeHero to new BackgroundMediaBlock for backward compatibility
  hero: HeroBlock,
  content: ContentBlock,
  contentMedia: ContentWithImageBlock,
  contentWithImage: ContentWithImageBlock, // Aliased for backward compatibility
  enhancedContentWithImage: EnhancedContentWithImageBlock,
  archive: ArchiveBlock,
  form: FormBlock, // Legacy mapping
  formBlock: FormBlock, // Match the exact slug from config
  CallToAction: CallToActionBlock, // Casing must match exactly with the slug in config
  callToAction: CallToActionBlock, // Legacy lowercase mapping for existing content
  callToAction2: CallToActionBlock, // Aliased to the same component
  cta3: CallToActionBlock, // Map old cta3 to consolidated component
  valueGrid: ValueGridBlock,
  textMedia: ContentWithImageBlock, // Another alias for backward compatibility
  mediaBlock: MediaBlock,
  featureGrid: FeatureGridBlock,
  // pricing: PricingBlock, // Uncomment when PricingBlock is available
  certifications: CertificationsBlock,
  certifications2: CertificationsBlock, // Mapped to the same component with different default props
  certificationsAlternate: CertificationsAlternateBlock, // Add the new alternate certifications block
  statsCounter: StatsCounterBlock, // Add the new stats counter block
  teamBio: TeamBioBlock, // Add the new team bio block
  slidingText: SlidingTextBlock,
  quote: QuoteBlock, // Add the quote block component
  materialsShowcase: MaterialsShowcaseBlock, // Add materials showcase block - slug must match the config
  table: TableBlock, // Add the new table block component
  timeline: TimelineBlock, // Add the new timeline block component
  faqAccordion: FAQAccordionBlock, // Add the FAQ accordion block
  tabs: TabsBlock, // Add the new tabs block with exact slug match
  beforeAfter: BeforeAfterBlock, // Add the new before/after comparison block
  breadcrumb: BreadcrumbBlock, // Add the new breadcrumb block
  'sticky-scroll-reveal': StickyScrollRevealBlock, // Add the new sticky scroll reveal block
  LayoutGrid: LayoutGridBlock, // Add the new layout grid block with exact slug match
  layoutGrid: LayoutGridBlock, // Add lowercase variant for backward compatibility
  stepperBlock: StepperBlock,
  visualTimelineBlock: VisualTimelineBlock,
  workflowProcessBlock: WorkflowProcessBlock,
  'workflow-process': WorkflowProcessBlock,
  processdiagram: ProcessDiagramBlock,
  'process-diagram': ProcessDiagramBlock,
  processDiagramBlock: ProcessDiagramBlock,
  approachFeatures: ApproachFeaturesBlock,
  'approach-features': ApproachFeaturesBlock,
  testimonials: TestimonialsBlockComponent,
  'testimonials-block': TestimonialsBlockComponent,
  dropCapStory: DropCapStoryBlock,
  partnersLogos: PartnersLogosBlock,
  carouselHero: CarouselHeroBlock,
  projectsShowcase: ProjectsShowcaseBlock,
  team: TeamBlock,
  savoirFaire: SavoirFaireBlock,
  services: ServicesBlock,
  projectSteps: ProjectStepsBlock,
}

// Define which block types use the column-based layout
const columnBlocks = ['content', 'certifications', 'slidingText']

// Map size values to grid column spans
const sizeToColSpan = {
  full: 'col-span-12',
  half: 'col-span-6',
  oneThird: 'col-span-4',
  twoThirds: 'col-span-8',
}

export const RenderBlocks: React.FC<{
  blocks: Page['layout'][0][]
  locale: string
}> = (props) => {
  const { blocks, locale } = props

  const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0

  if (hasBlocks) {
    // Group blocks into rows - create a new row whenever a non-column block is encountered
    const rows: Page['layout'][0][][] = []
    let currentRow: Page['layout'][0][] = []

    blocks.forEach((block) => {
      const blockType = block?.blockType as string

      // If it's a column block, add it to the current row
      if (blockType && columnBlocks.includes(blockType)) {
        currentRow.push(block)
      } else {
        // If current row has blocks, push it to rows and start a new row
        if (currentRow.length > 0) {
          rows.push([...currentRow])
          currentRow = []
        }
        // Add non-column block as its own row
        rows.push([block])
      }
    })

    // Add any remaining blocks in the current row
    if (currentRow.length > 0) {
      rows.push(currentRow)
    }

    return (
      <Fragment>
        {rows.map((row, rowIndex) => {
          // If row contains multiple column blocks, render them in a grid
          if (
            row.length > 1 &&
            row.every((block) => {
              const blockType = block?.blockType as string
              return blockType && columnBlocks.includes(blockType)
            })
          ) {
            return (
              <div className="container my-16" key={`row-${rowIndex}`}>
                <div className="grid grid-cols-12 gap-8">
                  {row.map((block, blockIndex) => {
                    const blockType = block?.blockType as string
                    const Component = blockType
                      ? blockComponents[blockType as keyof typeof blockComponents]
                      : null

                    // Determine column span based on size property
                    const size = (block as any)?.size || 'full'
                    const colSpanClass =
                      sizeToColSpan[size as keyof typeof sizeToColSpan] || 'col-span-12'

                    return Component ? (
                      <div className={`${colSpanClass}`} key={`block-${rowIndex}-${blockIndex}`}>
                        <Component {...(block as any)} locale={locale} disableInnerContainer />
                      </div>
                    ) : null
                  })}
                </div>
              </div>
            )
          } else {
            // For single blocks or non-column blocks, render normally
            const block = row[0]
            const blockType = block?.blockType as string

            if (blockType && blockType in blockComponents) {
              const Block = blockComponents[blockType as keyof typeof blockComponents]

              if (Block) {
                // Just render the block directly - our BlockWrapper will handle padding and container
                return <Block {...(block as any)} locale={locale} key={`row-${rowIndex}`} />
              }
            }
            return null
          }
        })}
      </Fragment>
    )
  }

  return null
}
