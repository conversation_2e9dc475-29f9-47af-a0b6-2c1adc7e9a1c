'use client'

import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { CheckCircle, AlertCircle, Loader2 } from 'lucide-react'

interface FilterStatusProps {
  isLoading: boolean
  hasResults: boolean
  resultCount: number
  locale: string
  className?: string
}

export const FilterStatus: React.FC<FilterStatusProps> = ({
  isLoading,
  hasResults,
  resultCount,
  locale,
  className = ''
}) => {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
            className="flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-700 rounded-full"
          >
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm font-medium">
              {locale === 'fr' ? 'Filtrage...' : 'Filtering...'}
            </span>
          </motion.div>
        ) : hasResults ? (
          <motion.div
            key="success"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
            className="flex items-center gap-2 px-4 py-2 bg-green-50 text-green-700 rounded-full"
          >
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm font-medium">
              {resultCount} {locale === 'fr' ? 'résultats' : 'results'}
            </span>
          </motion.div>
        ) : (
          <motion.div
            key="no-results"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.2 }}
            className="flex items-center gap-2 px-4 py-2 bg-orange-50 text-orange-700 rounded-full"
          >
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm font-medium">
              {locale === 'fr' ? 'Aucun résultat' : 'No results'}
            </span>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

interface FilterBadgeProps {
  label: string
  onRemove: () => void
  color?: 'red' | 'orange' | 'blue' | 'green'
  className?: string
}

export const FilterBadge: React.FC<FilterBadgeProps> = ({
  label,
  onRemove,
  color = 'red',
  className = ''
}) => {
  const colorClasses = {
    red: 'bg-gradient-to-r from-red-100 to-red-50 text-red-800 border-red-200 hover:from-red-200 hover:to-red-100',
    orange: 'bg-gradient-to-r from-orange-100 to-orange-50 text-orange-800 border-orange-200 hover:from-orange-200 hover:to-orange-100',
    blue: 'bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border-blue-200 hover:from-blue-200 hover:to-blue-100',
    green: 'bg-gradient-to-r from-green-100 to-green-50 text-green-800 border-green-200 hover:from-green-200 hover:to-green-100'
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8, x: -20 }}
      animate={{ opacity: 1, scale: 1, x: 0 }}
      exit={{ opacity: 0, scale: 0.8, x: -20 }}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      className={`
        inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium
        border shadow-sm transition-all duration-200 cursor-pointer
        ${colorClasses[color]} ${className}
      `}
    >
      <span>{label}</span>
      <button
        onClick={onRemove}
        className="hover:bg-black/10 rounded-full p-0.5 transition-colors duration-150"
        aria-label={`Remove ${label} filter`}
      >
        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </motion.div>
  )
}

interface FilterGridProps {
  children: React.ReactNode
  isLoading?: boolean
  className?: string
}

export const FilterGrid: React.FC<FilterGridProps> = ({
  children,
  isLoading = false,
  className = ''
}) => {
  return (
    <motion.div
      layout
      className={`grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 ${className}`}
      style={{ opacity: isLoading ? 0.6 : 1 }}
      transition={{ duration: 0.3 }}
    >
      <AnimatePresence mode="popLayout">
        {children}
      </AnimatePresence>
    </motion.div>
  )
}

interface FilterCardProps {
  children: React.ReactNode
  isVisible?: boolean
  delay?: number
  className?: string
}

export const FilterCard: React.FC<FilterCardProps> = ({
  children,
  isVisible = true,
  delay = 0,
  className = ''
}) => {
  if (!isVisible) return null

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{ 
        duration: 0.4, 
        delay,
        type: 'spring',
        stiffness: 100,
        damping: 15
      }}
      whileHover={{ 
        y: -4,
        transition: { duration: 0.2 }
      }}
      className={`
        bg-white rounded-lg shadow-sm border border-gray-200 
        hover:shadow-md hover:border-gray-300 
        transition-all duration-200 ${className}
      `}
    >
      {children}
    </motion.div>
  )
}

interface FilterSkeletonProps {
  count?: number
  className?: string
}

export const FilterSkeleton: React.FC<FilterSkeletonProps> = ({
  count = 6,
  className = ''
}) => {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 ${className}`}>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
        >
          <div className="animate-pulse">
            <div className="w-full h-48 bg-gray-200 rounded-lg mb-4"></div>
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
            <div className="flex gap-2">
              <div className="h-6 bg-gray-200 rounded-full w-16"></div>
              <div className="h-6 bg-gray-200 rounded-full w-20"></div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

interface FilterEmptyStateProps {
  title: string
  description: string
  onReset?: () => void
  resetLabel?: string
  className?: string
}

export const FilterEmptyState: React.FC<FilterEmptyStateProps> = ({
  title,
  description,
  onReset,
  resetLabel = 'Reset Filters',
  className = ''
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
      className={`text-center py-16 ${className}`}
    >
      <div className="max-w-md mx-auto">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3, delay: 0.2 }}
          className="w-16 h-16 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"
        >
          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        </motion.div>
        
        <motion.h3
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.3 }}
          className="text-xl font-semibold text-gray-900 mb-2"
        >
          {title}
        </motion.h3>
        
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.4 }}
          className="text-gray-600 mb-6"
        >
          {description}
        </motion.p>
        
        {onReset && (
          <motion.button
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.5 }}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onReset}
            className="px-6 py-3 bg-red-500 text-white rounded-lg font-medium hover:bg-red-600 transition-colors duration-200 shadow-sm hover:shadow-md"
          >
            {resetLabel}
          </motion.button>
        )}
      </div>
    </motion.div>
  )
}
