import type { Block } from 'payload'

export const Testimonial: Block = {
  slug: 'testimonial',
  labels: {
    singular: 'Testimonial',
    plural: 'Testimonials',
  },
  fields: [
    {
      name: 'quote',
      type: 'textarea',
      localized: true,
      required: true,
    },
    {
      name: 'author',
      type: 'text',
      localized: true,
      required: true,
    },
    {
      name: 'position',
      type: 'text',
      localized: true,
    },
    {
      name: 'company',
      type: 'text',
      localized: true,
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
      localized: true,
    },
    {
      name: 'backgroundColor',
      type: 'select',
      localized: true,
      options: [
        { label: 'White', value: 'white' },
        { label: 'Light Gray', value: 'lightGray' },
      ],
      defaultValue: 'white',
    },
    {
      name: 'layout',
      type: 'select',
      localized: true,
      options: [
        { label: 'Image Left', value: 'imageLeft' },
        { label: 'Image Right', value: 'imageRight' },
        { label: 'Image Top', value: 'imageTop' },
      ],
      defaultValue: 'imageLeft',
    },
  ],
}
