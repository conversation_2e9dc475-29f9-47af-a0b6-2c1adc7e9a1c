'use client'
import { useTheme } from '@/providers/Theme'
import { useHeaderTheme } from '@/providers/HeaderTheme'
import Link from 'next/link'
import React, { useEffect, useState, useCallback } from 'react'
import type { Config } from 'src/payload-types'
import type { Header } from '@/payload-types'

import { Logo } from '@/components/Logo/Logo'
import { HeaderNav } from './Nav'

interface HeaderClientProps {
  data: Header
  locale: Config['locale']
}

export const HeaderClient: React.FC<HeaderClientProps> = ({ data, locale }) => {
  const { theme } = useTheme()
  const { headerTheme } = useHeaderTheme()
  const [isScrolled, setIsScrolled] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [mounted, setMounted] = useState(false)

  const handleScroll = useCallback(() => {
    const scrollY = window.scrollY
    const threshold = 50
    const heroHeight = window.innerHeight // Full screen hero section

    setIsScrolled(scrollY > threshold)

    // Calculate scroll progress through hero section
    const progress = Math.min(scrollY / heroHeight, 1)
    setScrollProgress(progress)
  }, [])

  useEffect(() => {
    setMounted(true)
    handleScroll() // Set initial state

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [handleScroll])

  return (
    <header
      className={`fixed top-0 left-0 right-0 w-full z-50 transition-all duration-500 ease-out ${isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-xl border-b border-gray-200/50 text-gray-900'
        : 'bg-transparent backdrop-blur-none shadow-none border-none text-white'
        }`}
      style={{
        background: isScrolled
          ? 'rgba(255, 255, 255, 0.7)'
          : `linear-gradient(180deg, rgba(0,0,0,${0.7 - scrollProgress * 0.4}) 0%, transparent 100%)`,
        transform: mounted ? `scale(${1 - scrollProgress * 0.01})` : 'scale(1)',
        transition: 'all 500ms cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 100ms ease-out',
      }}
      {...(mounted && theme ? { 'data-theme': theme } : {})}
    >
      <div className={`container mx-auto px-4 sm:px-6 lg:px-8 transition-all duration-500 ease-out flex items-center justify-between`}>

        {/* Logo */}
        <Link
          href={`/${locale}`}
          className="flex items-center gap-2 transition-transform duration-300 hover:scale-105 z-50 relative"
        >
          <Logo loading="eager" priority="high" />
        </Link>

        {/* Navigation */}
        <div className="flex items-center">
          <HeaderNav data={data} isScrolled={isScrolled} headerTheme={headerTheme} locale={locale} />
        </div>
      </div>
    </header>
  )
}
