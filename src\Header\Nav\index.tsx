'use client'

import React, { useState, useEffect } from 'react'
import { AnimatePresence, motion } from 'framer-motion'
import { usePathname } from 'next/navigation'
import type { Header as HeaderType } from '@/payload-types'
import type { Theme } from '@/providers/Theme/types'
import type { Config } from 'src/payload-types'
import { CMSLink } from '@/components/Link'
import { LanguageSelector } from '@/components/LanguageSelector'
import { Menu, X } from 'lucide-react'

interface HeaderNavProps {
  data: HeaderType
  isScrolled: boolean
  headerTheme?: Theme | null
  locale: Config['locale']
}

export const HeaderNav: React.FC<HeaderNavProps> = ({ data, isScrolled, locale }) => {
  const navItems = data?.navItems || []
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const navCtas = data?.navCtas || []
  const pathname = usePathname()

  useEffect(() => {
    setMounted(true)
  }, [])

  // Close menu when resizing to desktop size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && isMenuOpen) {
        setIsMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [isMenuOpen])

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (isMenuOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = ''
    }

    return () => {
      document.body.style.overflow = ''
    }
  }, [isMenuOpen])

  return (
    <>
      {/* Desktop Navigation */}
      <nav className="hidden md:flex gap-3 items-center">
        {navItems.map(({ link }, i) => {
          // Check if this nav item is active based on current path
          const isActive = mounted && pathname === link?.url

          return (
            <span
              key={i}
              className="nav-link-wrapper"
            >
              <CMSLink
                {...link}
                locale={locale}
                appearance="link"
                className={`
                  text-lg font-medium tracking-wide transition-all duration-300 nav-adaptive-link
                  ${mounted && !isScrolled ? 'text-white' : ''}
                  ${isActive
                    ? 'text-destructive font-semibold'
                    : mounted && !isScrolled
                      ? 'hover:text-destructive hover:font-semibold text-white'
                      : 'hover:text-destructive hover:font-semibold'
                  }
                `}
              />
            </span>
          )
        })}
        {/* <span className="nav-link-wrapper">
          <Link href="/search">
            <span className="sr-only">Search</span>
            <SearchIcon className="w-5" />
          </Link>
        </span> */}

        {navCtas.map(({ link }, i) => {
          return (
            <div
              key={i}
              className={`transition-opacity duration-300 `}
            >
              <CMSLink {...link} locale={locale} />
            </div>
          )
        })}

        {/* Language Selector */}
        <div className="ml-3 pl-3 border-l border-gray-300/30">
          <LanguageSelector locale={locale} />
        </div>
      </nav>

      {/* Mobile Burger Menu Button */}
      <button
        className={`
          md:hidden flex items-center justify-center p-3 z-50 relative rounded-xl transition-all duration-300
          ${isMenuOpen
            ? 'bg-white text-gray-900 shadow-lg border-2 border-gray-200'
            : isScrolled
              ? 'bg-gray-100 text-gray-900 hover:bg-gray-200 border-2 border-gray-200'
              : 'bg-white/20 text-white hover:bg-white/30 backdrop-blur-sm border-2 border-white/30'
          }
        `}
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        aria-expanded={isMenuOpen}
        aria-label="Toggle menu"
      >
        <motion.div
          animate={{ rotate: isMenuOpen ? 90 : 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        >
          {isMenuOpen ? (
            <X className="w-7 h-7" />
          ) : (
            <Menu className="w-7 h-7" />
          )}
        </motion.div>
      </button>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              className="fixed inset-0 bg-black/50 z-30 md:hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              onClick={() => setIsMenuOpen(false)}
            />

            {/* Menu Panel - Full Screen Mobile */}
            <motion.div
              className="fixed inset-0 w-full h-screen bg-white z-40 overflow-y-auto"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }}
            >
              {/* Menu Header */}
              <div className="flex items-center justify-between p-6 pt-8 border-b-2 border-gray-200 bg-white sticky top-0 z-10 shadow-sm">
                <div className="flex items-center gap-3">
                </div>
              </div>

              {/* Menu Content */}
              <div className="min-h-screen bg-white pb-20">
                {/* Navigation Items */}
                <nav className="px-6 py-8">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 mb-8">
                      <div className="w-1 h-6 bg-gray-400 rounded-full"></div>
                      <h3 className="text-lg font-semibold text-gray-600 uppercase tracking-wide">Navigation</h3>
                    </div>
                    {navItems.map(({ link }, i) => {
                      const isActive = mounted && pathname === link?.url

                      return (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, x: 30 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{
                            delay: i * 0.1 + 0.2,
                            duration: 0.4,
                            ease: [0.25, 0.46, 0.45, 0.94]
                          }}
                          onClick={() => setIsMenuOpen(false)}
                          className={`
                            group block px-6 py-5 rounded-2xl text-2xl font-bold transition-all duration-300 cursor-pointer border-2 shadow-sm hover:shadow-lg
                            ${isActive
                              ? 'bg-gradient-to-r from-red-50 to-red-100 text-destructive border-red-300 shadow-lg transform scale-[1.02]'
                              : 'text-gray-900 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 hover:text-destructive border-gray-200 hover:border-red-200 hover:transform hover:scale-[1.01]'
                            }
                          `}
                        >
                          <div className="flex items-center justify-between">
                            <CMSLink
                              locale={locale}
                              {...link}
                              appearance="link"
                              className="block flex-1 text-inherit no-underline"
                            />
                            <motion.div
                              className={`w-2 h-2 rounded-full transition-all duration-300 ${isActive ? 'bg-red-500' : 'bg-gray-300 group-hover:bg-red-400'
                                }`}
                              animate={{ scale: isActive ? 1.2 : 1 }}
                            />
                          </div>
                        </motion.div>
                      )
                    })}
                  </div>
                </nav>

                {/* CTAs Section */}
                {navCtas.length > 0 && (
                  <div className="px-6 py-8 border-t-4 border-gray-200 bg-gradient-to-br from-gray-50 via-gray-100 to-gray-50">
                    <div className="space-y-6">
                      <div className="flex items-center gap-3 mb-8">
                        <div className="w-1 h-6 bg-red-400 rounded-full"></div>
                        <h3 className="text-lg font-semibold text-gray-700 uppercase tracking-wide">Actions Rapides</h3>
                      </div>
                      {navCtas.map(({ link }, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, y: 30 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{
                            delay: (navItems.length + i) * 0.1 + 0.3,
                            duration: 0.4,
                            ease: [0.25, 0.46, 0.45, 0.94]
                          }}
                          onClick={() => setIsMenuOpen(false)}
                          className="block"
                        >
                          <div className="relative group">
                            <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl blur-sm opacity-75 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div className="relative p-2 bg-gradient-to-r from-red-500 to-red-600 rounded-2xl shadow-xl">
                              <CMSLink
                                locale={locale}
                                {...link}
                                className="block w-full text-xl font-bold text-white bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 rounded-xl transition-all duration-300 text-center shadow-inner transform hover:scale-[1.02]"
                              />
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Language Selector Section */}
                <div className="px-6 py-8 border-t-4 border-gray-200 bg-gradient-to-br from-gray-50 via-gray-100 to-gray-50">
                  <div className="space-y-6">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="w-1 h-6 bg-blue-400 rounded-full"></div>
                      <h3 className="text-lg font-semibold text-gray-700 uppercase tracking-wide">Language</h3>
                    </div>
                    <div className="bg-white rounded-2xl p-4 shadow-sm border-2 border-gray-200">
                      <LanguageSelector locale={locale} />
                    </div>
                  </div>
                </div>

                {/* Footer Spacing */}
                <div className="h-20"></div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}
