'use client'

import React from 'react'
import { Media } from '@/components/Media'
import { cn } from '@/utilities/ui'
import RichText from '@/components/RichText'
import { CMSLink } from '@/components/Link'
import type { Locale } from '../../../middleware'
import './styles.css'

// Local type for props since payload-types not updated yet
type BackgroundMediaBlockProps = {
  // Media props
  mediaType?: 'image' | 'video'
  backgroundMedia?: any
  videoUrl?: string
  videoOptions?: {
    muted?: boolean
    autoplay?: boolean
    loop?: boolean
    controls?: boolean
  }
  backgroundPosition?:
  | 'center'
  | 'top'
  | 'bottom'
  | 'left'
  | 'right'
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'

  // Sizing props
  height?: 'small' | 'medium' | 'large'

  // Content props
  richText?: any
  enableLink?: boolean
  primaryLink?: {
    type?: 'custom' | 'reference'
    label?: string
    url?: string
    reference?: any
    newTab?: boolean
    appearance?: 'primary' | 'secondary' | 'link' | 'default' | 'none' | string
  }
  enableRightLink?: boolean
  secondaryLink?: {
    type?: 'custom' | 'reference'
    label?: string
    url?: string
    reference?: any
    newTab?: boolean
    appearance?: 'primary' | 'secondary' | 'link' | 'default' | 'none' | string
  }
  alignment?: 'left' | 'center' | 'right'

  // Overlay settings - flattened
  overlayType?: 'color' | 'gradient-tb' | 'gradient-lr' | 'gradient-radial' | 'none'
  overlayColor?: string
  secondaryOverlayColor?: string
  overlayOpacity?: number

  // Content positioning - flattened
  verticalAlignment?: 'top' | 'center' | 'bottom'
  contentWidth?: 'narrow' | 'medium' | 'wide' | 'full'
  contentPadding?: 'none' | 'small' | 'medium' | 'large'

  // Visual enhancements - flattened
  enableParallax?: boolean
  parallaxIntensity?: 'subtle' | 'medium' | 'strong'
  maskType?: 'none' | 'rounded' | 'diagonal-tl' | 'diagonal-tr' | 'wavy-top' | 'wavy-bottom'
  borderType?: 'none' | 'solid' | 'dashed' | 'double'
  borderColor?: 'primary' | 'secondary' | 'white' | 'black' | 'gray'
  borderWidth?: 'thin' | 'medium' | 'thick'

  // Preset selection
  preset?: 'custom' | 'sustainabilityBanner'

  // Sustainability banner specific props
  sustainabilityCta?: {
    label?: string
    url?: string
  }

  // Standard block props
  blockName?: string
  id?: string
  className?: string
  disableInnerContainer?: boolean
}

// Helper function to safely serialize objects by removing circular references
function safeSerialize(obj: any): any {
  const seen = new WeakSet()
  return JSON.parse(
    JSON.stringify(obj, (key, value) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          return // Remove circular reference
        }
        seen.add(value)
      }
      return value
    }),
  )
}

export const BackgroundMediaBlock: React.FC<BackgroundMediaBlockProps & { locale: Locale }> = (props) => {
  const {
    // Media props
    mediaType = 'image',
    backgroundMedia,
    videoUrl = '',
    videoOptions = {},
    backgroundPosition = 'center',

    // Sizing props
    height: heightProp = 'medium',

    // Content props
    richText,
    enableLink = false,
    primaryLink = {},
    enableRightLink = false,
    secondaryLink = {},
    alignment: alignmentProp = 'center',

    // Overlay settings - flattened
    overlayType = 'color',
    overlayColor = '#000000',
    secondaryOverlayColor = '#555555',
    overlayOpacity = 50,

    // Content positioning - flattened
    verticalAlignment = 'center',
    contentWidth = 'medium',
    contentPadding = 'medium',

    // Visual enhancements - flattened
    enableParallax = false,
    parallaxIntensity = 'medium',
    maskType = 'none',
    borderType = 'none',
    borderColor = 'primary',
    borderWidth = 'medium',

    // Preset selection
    preset = 'custom',
    sustainabilityCta = {},

    // Standard block props
    blockName,
    id,
    className,
    disableInnerContainer,

    // Locale
    locale,
  } = props

  // Log the values to debug
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      console.log('BackgroundMedia Block Props:', {
        height: heightProp,
        alignment: alignmentProp,
        contentWidth,
        verticalAlignment,
      })
    }
  }, [heightProp, alignmentProp, contentWidth, verticalAlignment])

  const heightClasses = {
    small: 'h-64 md:h-72', // 16rem on mobile, 18rem on desktop
    medium: 'h-[28rem] md:h-[32rem]', // 20rem on mobile, 24rem on desktop
    large: 'h-[40rem] md:h-[48rem]', // 28rem on mobile, 32rem on desktop
  } as const

  const justifyClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end',
  } as const

  // Use the destructured props directly instead of accessing props.X
  const height = heightProp as keyof typeof heightClasses
  const textAlign = alignmentProp as keyof typeof justifyClasses

  const textAlignClass = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right',
  }[textAlign]

  // Get width class based on contentWidth setting
  const getWidthClass = (): string => {
    switch (contentWidth) {
      case 'narrow':
        return 'max-w-md mx-auto'
      case 'medium':
        return 'max-w-2xl mx-auto'
      case 'wide':
        return 'max-w-4xl mx-auto'
      case 'full':
        return 'w-full'
      default:
        return 'max-w-2xl mx-auto'
    }
  }

  // Get parallax effect classes based on settings
  const getParallaxClasses = (): string => {
    if (!enableParallax) return ''

    // Base class for all parallax effects
    const baseClass = 'transition-transform duration-\[1.5s\] ease-out'

    // Intensity modifiers
    const intensityClass =
      {
        subtle: 'parallax-subtle',
        medium: 'parallax-medium',
        strong: 'parallax-strong',
      }[parallaxIntensity] || 'parallax-medium'

    return cn(baseClass, intensityClass)
  }

  // Get mask classes based on maskType
  const getMaskClasses = (): string => {
    switch (maskType) {
      case 'rounded':
        return 'rounded-2xl overflow-hidden'
      case 'diagonal-tl':
        return 'mask-diagonal-tl'
      case 'diagonal-tr':
        return 'mask-diagonal-tr'
      case 'wavy-top':
        return 'mask-wavy-top'
      case 'wavy-bottom':
        return 'mask-wavy-bottom'
      default:
        return ''
    }
  }

  // Special rendering for the sustainability banner preset
  if (preset === 'sustainabilityBanner') {
    return (
      <div
        id={id}
        className={cn(
          'relative w-full overflow-hidden',
          heightClasses['large'], // Always use large height for sustainability banner
          className,
        )}
      >
        {/* Background */}
        {backgroundMedia ? (
          <Media
            resource={backgroundMedia}
            fill
            imgClassName={cn(
              'object-cover absolute inset-0',
              'object-center', // Always center for the sustainability banner
              'parallax-subtle', // Subtle parallax effect
            )}
          />
        ) : null}

        {/* Custom Overlay - Always use gradient for sustainability banner */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background:
              'linear-gradient(to bottom, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.3) 100%)',
            opacity: 0.6,
          }}
        />

        {/* Content Container */}
        <div
          className={cn(
            'absolute inset-0 flex flex-col items-center justify-center z-10', // Always center vertically
          )}
        >
          <div
            className={cn(
              'max-w-4xl mx-auto', // Always use wide width
              'px-6 py-12 md:px-12', // Large padding
              'flex flex-col items-start', // Always align items to start
              'text-left', // Always left align text
            )}
          >
            {richText && (
              <RichText
                data={richText}
                enableGutter={false}
                className="text-white m-0 sustainability-rich-text"
              />
            )}
            {primaryLink && primaryLink.label && primaryLink.appearance !== 'none' && (
              <div className="mt-8">
                <CMSLink
                  type={primaryLink.type}
                  url={primaryLink.url}
                  reference={
                    primaryLink.reference
                      ? {
                        relationTo: primaryLink.reference.relationTo,
                        value: primaryLink.reference.value,
                      }
                      : undefined
                  }
                  newTab={primaryLink.newTab}
                  label={primaryLink.label}
                  locale={locale}
                  className="bg-black hover:bg-opacity-80 text-white font-semibold py-3 px-6 rounded-md transition-all duration-300"
                />
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Get border classes based on borderType settings
  const getBorderClasses = (): string => {
    if (borderType === 'none') return ''

    // Base classes for all border types
    let borderClass = ''

    // Border style
    switch (borderType) {
      case 'solid':
        borderClass += ' border-solid'
        break
      case 'dashed':
        borderClass += ' border-dashed'
        break
      case 'double':
        borderClass += ' border-double'
        break
    }

    // Border color
    const borderColorClass =
      {
        primary: 'border-primary',
        secondary: 'border-secondary',
        white: 'border-white',
        black: 'border-black',
        gray: 'border-gray-400',
      }[borderColor] || 'border-primary'

    // Border width
    const borderWidthClass =
      {
        thin: 'border',
        medium: 'border-2',
        thick: 'border-4',
      }[borderWidth] || 'border-2'

    return cn(borderClass, borderColorClass, borderWidthClass)
  }

  // Get padding class based on contentPadding setting
  const getPaddingClass = (): string => {
    switch (contentPadding) {
      case 'none':
        return ''
      case 'small':
        return 'px-4 py-4'
      case 'medium':
        return 'px-6 py-8'
      case 'large':
        return 'px-8 py-12'
      default:
        return 'px-6 py-8'
    }
  }

  // Get vertical alignment class based on verticalAlignment setting
  const getVerticalAlignmentClass = (): string => {
    switch (verticalAlignment) {
      case 'top':
        return 'justify-start'
      case 'center':
        return 'justify-center'
      case 'bottom':
        return 'justify-end'
      default:
        return 'justify-center'
    }
  }

  // Determine the overlay style based on type
  const getOverlayStyle = () => {
    // Create a new object for style (don't modify or reference existing objects)
    const style: React.CSSProperties = {
      position: 'absolute',
      top: 0,
      left: 0,
      width: '100%',
      height: '100%',
    }

    const opacityValue = overlayOpacity / 100

    switch (overlayType) {
      case 'color':
        style.backgroundColor = overlayColor
        style.opacity = opacityValue
        break
      case 'gradient-tb':
        style.background = `linear-gradient(to bottom, ${overlayColor} 0%, ${secondaryOverlayColor} 100%)`
        style.opacity = opacityValue
        break
      case 'gradient-lr':
        style.background = `linear-gradient(to right, ${overlayColor} 0%, ${secondaryOverlayColor} 100%)`
        style.opacity = opacityValue
        break
      case 'gradient-radial':
        style.background = `radial-gradient(circle, ${overlayColor} 0%, ${secondaryOverlayColor} 100%)`
        style.opacity = opacityValue
        break
    }

    return style
  }

  // Determine the background position class for images
  const getBackgroundPositionClass = () => {
    switch (backgroundPosition) {
      case 'top':
        return 'object-top'
      case 'bottom':
        return 'object-bottom'
      case 'left':
        return 'object-left'
      case 'right':
        return 'object-right'
      case 'top-left':
        return 'object-left-top'
      case 'top-right':
        return 'object-right-top'
      case 'bottom-left':
        return 'object-left-bottom'
      case 'bottom-right':
        return 'object-right-bottom'
      case 'center':
      default:
        return 'object-center'
    }
  }

  // Determine if the video URL is from YouTube, Vimeo, or a direct file
  const getVideoEmbedUrl = (url: string) => {
    if (!url) return null

    // YouTube URL patterns
    const ytRegex =
      /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/
    const ytMatch = url.match(ytRegex)
    if (ytMatch) {
      const videoId = ytMatch[1]
      const options = videoOptions || {}
      let embedUrl = `https://www.youtube.com/embed/${videoId}?modestbranding=1&rel=0`
      if (options.autoplay) embedUrl += '&autoplay=1'
      if (options.muted) embedUrl += '&mute=1'
      if (options.loop) embedUrl += '&loop=1&playlist=' + videoId
      return embedUrl
    }

    // Vimeo URL patterns
    const vimeoRegex =
      /(?:https?:\/\/)?(?:www\.)?(?:vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|)(\d+)(?:|\/\?))/
    const vimeoMatch = url.match(vimeoRegex)
    if (vimeoMatch) {
      const videoId = vimeoMatch[2]
      const options = videoOptions || {}
      let embedUrl = `https://player.vimeo.com/video/${videoId}?title=0&byline=0&portrait=0`
      if (options.autoplay) embedUrl += '&autoplay=1'
      if (options.muted) embedUrl += '&muted=1'
      if (options.loop) embedUrl += '&loop=1'
      if (options.controls === false) embedUrl += '&controls=0'
      return embedUrl
    }

    // If it's not YouTube or Vimeo, assume it's a direct file URL
    return url
  }

  // UseEffect for parallax scrolling
  /* eslint-disable react-hooks/rules-of-hooks */
  React.useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined') return

    // Function to handle scroll and apply transform
    const handleScroll = () => {
      if (!enableParallax) return

      // Find the current block's parallax container
      const blockElement = document.getElementById(id || '')
      if (!blockElement) return

      const parallaxContainer = blockElement.querySelector(
        '.parallax-subtle, .parallax-medium, .parallax-strong',
      )
      if (!parallaxContainer) return

      // Get element's position relative to viewport
      const rect = blockElement.getBoundingClientRect()
      const windowHeight = window.innerHeight

      // Calculate how far the element is from the center of the viewport as a percentage
      const viewportMiddle = windowHeight / 2
      const elementMiddle = rect.top + rect.height / 2
      const distanceFromCenter = elementMiddle - viewportMiddle
      const windowOffset = distanceFromCenter / windowHeight

      // Apply transform based on intensity
      let translateY = 0
      if (parallaxContainer.classList.contains('parallax-subtle')) {
        translateY = windowOffset * 10 // Subtle movement (10% of distance)
      } else if (parallaxContainer.classList.contains('parallax-strong')) {
        translateY = windowOffset * 30 // Strong movement (30% of distance)
      } else {
        translateY = windowOffset * 20 // Medium movement (20% of distance)
      }

      // Apply the transform
      ; (parallaxContainer as HTMLElement).style.transform = `translateY(${translateY}px)`
    }

    // Initial call and add event listener
    handleScroll()
    window.addEventListener('scroll', handleScroll)

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
    }
  }, [enableParallax, parallaxIntensity, id])

  // Render the appropriate background based on mediaType
  const renderBackground = () => {
    if (mediaType === 'video' && videoUrl) {
      const embedUrl = getVideoEmbedUrl(videoUrl)

      if ((embedUrl && embedUrl.includes('youtube.com')) || embedUrl?.includes('vimeo.com')) {
        // For YouTube and Vimeo, use an iframe
        return (
          <div
            className={cn(
              'absolute inset-0 w-full h-full',
              enableParallax ? getParallaxClasses() : '',
            )}
          >
            <iframe
              src={embedUrl}
              className="absolute inset-0 w-full h-full"
              frameBorder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
            />
          </div>
        )
      } else if (embedUrl) {
        // For direct video files
        const { muted = true, autoplay = true, loop = true, controls = false } = videoOptions || {}
        return (
          <video
            src={embedUrl}
            className={cn(
              'absolute inset-0 w-full h-full object-cover',
              enableParallax ? getParallaxClasses() : '',
            )}
            muted={muted}
            autoPlay={autoplay}
            loop={loop}
            controls={controls}
            playsInline
          />
        )
      }
    }

    // Default to image background
    if (!backgroundMedia) return null

    // For parallax effect, we need to add extra height to the container
    if (enableParallax) {
      // Calculate extra height percentage based on parallax intensity
      const extraHeight =
        {
          subtle: 15,
          medium: 25,
          strong: 40,
        }[parallaxIntensity] || 25

      return (
        <div
          className={cn('absolute inset-0 overflow-hidden')}
          style={{
            height: `calc(100% + ${extraHeight}%)`,
            top: `-${extraHeight / 2}%`, // Center the extra height
          }}
        >
          <Media
            resource={backgroundMedia}
            fill
            imgClassName={cn(
              'object-cover w-full h-full',
              getBackgroundPositionClass(),
              getParallaxClasses(),
            )}
          />
        </div>
      )
    }

    // Regular image without parallax
    return (
      <Media
        resource={backgroundMedia}
        fill
        imgClassName={cn('object-cover absolute inset-0', getBackgroundPositionClass())}
      />
    )
  }

  // SVG definitions for wavy masks
  const renderSVGMasks = () => (
    <div className="svg-defs">
      <svg width="0" height="0" xmlns="http://www.w3.org/2000/svg">
        <defs>
          {/* Wavy top mask */}
          <clipPath id="wavy-top-mask" clipPathUnits="objectBoundingBox">
            <path d="M0,0.2 C0.2,0.1 0.4,0.3 0.6,0.2 C0.8,0.1 1,0.15 1,0.15 L1,1 L0,1 Z" />
          </clipPath>
          {/* Wavy bottom mask */}
          <clipPath id="wavy-bottom-mask" clipPathUnits="objectBoundingBox">
            <path d="M0,0 L1,0 L1,0.85 C0.8,0.9 0.6,0.7 0.4,0.8 C0.2,0.9 0,0.85 0,0.85 Z" />
          </clipPath>
        </defs>
      </svg>
    </div>
  )

  // Helper function to render a link with proper appearance handling
  const renderLink = (link: any, isSecondary = false) => {
    if (!link || !link.label || link.appearance === 'none') return null

    // Map 'default' appearance to appropriate button style based on available variants
    let appearance = link.appearance
    if (appearance === 'default') {
      appearance = isSecondary ? 'secondary' : 'default'
    } else if (appearance === 'primary') {
      appearance = 'default' // Map 'primary' to 'default' which is the primary button style
    }

    return (
      <CMSLink
        type={link.type}
        url={link.url}
        reference={
          link.reference
            ? {
              relationTo: link.reference.relationTo,
              value: link.reference.value,
            }
            : undefined
        }
        newTab={link.newTab}
        label={link.label}
        appearance={appearance}
        locale={locale}
      />
    )
  }

  return (
    <div
      id={id}
      className={cn(
        'relative w-full overflow-hidden',
        heightClasses[height],
        getMaskClasses(),
        getBorderClasses(),
        className,
      )}
    >
      {/* SVG Definitions for masks */}
      {maskType === 'wavy-top' || maskType === 'wavy-bottom' ? renderSVGMasks() : null}

      {/* Background */}
      {renderBackground()}

      {/* Custom Overlay based on settings */}
      {overlayType !== 'none' && <div style={getOverlayStyle()} />}

      {/* Content Container */}
      <div
        className={cn(
          'absolute inset-0 flex flex-col items-center z-10',
          getVerticalAlignmentClass(),
        )}
      >
        <div
          className={cn(
            getWidthClass(),
            getPaddingClass(),
            'flex flex-col',
            textAlign === 'left'
              ? 'items-start'
              : textAlign === 'right'
                ? 'items-end'
                : 'items-center',
            textAlignClass,
          )}
        >
          {richText && <RichText data={richText} enableGutter={false} className="text-white m-0" />}
          {(enableLink || enableRightLink) && (
            <div className={cn('mt-4 flex items-center gap-x-4', justifyClasses[textAlign])}>
              {enableLink && primaryLink && renderLink(primaryLink)}
              {enableRightLink && secondaryLink && renderLink(secondaryLink, true)}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
