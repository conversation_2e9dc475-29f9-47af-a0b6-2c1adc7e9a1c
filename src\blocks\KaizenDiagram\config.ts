import type { Block } from 'payload'

export const KaizenDiagram: Block = {
  slug: 'kaizenDiagram',
  labels: {
    singular: 'Kaizen Diagram',
    plural: 'Kaizen Diagrams',
  },
  fields: [
    {
      name: 'heading',
      type: 'text',
      localized: true,
    },
    {
      name: 'description',
      type: 'textarea',
      localized: true,
    },
    {
      name: 'image',
      type: 'upload',
      relationTo: 'media',
      localized: true,
    },
    {
      name: 'steps',
      type: 'array',
      localized: true,
      fields: [
        {
          name: 'title',
          type: 'text',
          localized: true,
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
          localized: true,
        },
        {
          name: 'color',
          type: 'text',
          localized: true,
          admin: {
            description: 'Color for this step (e.g., #FF0000 or red)',
          },
          defaultValue: '#FF0000',
        },
      ],
    },
    {
      name: 'layout',
      type: 'select',
      localized: true,
      options: [
        { label: 'Vertical', value: 'vertical' },
        { label: 'Horizontal', value: 'horizontal' },
        { label: 'Circular', value: 'circular' },
      ],
      defaultValue: 'vertical',
    },
  ],
}
