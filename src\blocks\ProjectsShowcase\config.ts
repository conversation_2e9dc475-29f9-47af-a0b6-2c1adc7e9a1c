import type { Block } from 'payload'

export const ProjectsShowcase: Block = {
    slug: 'projectsShowcase',
    interfaceName: 'ProjectsShowcaseBlock',
    labels: {
        singular: 'Projects Showcase',
        plural: 'Projects Showcase Blocks',
    },
    fields: [
        {
            name: 'title',
            type: 'text',
            label: 'Section Title',
            localized: true,
            admin: {
                description: 'Main heading for the projects section (e.g., "Our Projects", "Recent Work")',
            },
        },
        {
            name: 'subtitle',
            type: 'textarea',
            label: 'Section Subtitle',
            localized: true,
            admin: {
                description: 'Optional description or introduction text',
            },
        },
        {
            name: 'projects',
            type: 'array',
            label: 'Projects',
            localized: true,
            labels: {
                singular: 'Project',
                plural: 'Projects',
            },
            fields: [
                {
                    name: 'image',
                    type: 'upload',
                    label: 'Project Image',
                    relationTo: 'media',
                    localized: true,
                    required: true,
                    admin: {
                        description: 'Main project image (recommended: 1200x800 or 16:9 aspect ratio)',
                    },
                },
                {
                    name: 'title',
                    type: 'text',
                    label: 'Project Title',
                    localized: true,
                    required: true,
                    admin: {
                        description: 'Name of the project',
                    },
                },
                {
                    name: 'description',
                    type: 'textarea',
                    label: 'Project Description',
                    localized: true,
                    admin: {
                        description: 'Brief description of the project (2-3 sentences)',
                    },
                },
                {
                    name: 'category',
                    type: 'text',
                    label: 'Project Category',
                    localized: true,
                    admin: {
                        description: 'Category or type of project (e.g., "Kitchen Design", "Restaurant", "Hotel")',
                    },
                },
                {
                    name: 'client',
                    type: 'text',
                    label: 'Client Name',
                    localized: true,
                    admin: {
                        description: 'Name of the client or company',
                    },
                },
                {
                    name: 'location',
                    type: 'text',
                    label: 'Project Location',
                    localized: true,
                    admin: {
                        description: 'City, country, or specific location',
                    },
                },
                {
                    name: 'completionDate',
                    type: 'text',
                    label: 'Completion Date',
                    localized: true,
                    admin: {
                        description: 'When the project was completed (e.g., "March 2024", "2023")',
                    },
                },
                {
                    name: 'projectUrl',
                    type: 'text',
                    label: 'Project Details URL',
                    localized: true,
                    admin: {
                        description: 'Link to the detailed project page (e.g., /projects/project-name)',
                    },
                },
                {
                    name: 'featured',
                    type: 'checkbox',
                    label: 'Featured Project',
                    localized: true,
                    defaultValue: false,
                    admin: {
                        description: 'Mark as featured to display prominently',
                    },
                },
            ],
            admin: {
                initCollapsed: false,
                description: 'Add your B2B projects with images, details, and links',
            },
            minRows: 1,
            defaultValue: [
                {
                    title: 'Modern Restaurant Kitchen',
                    description: 'Complete kitchen design and installation for a high-end restaurant featuring state-of-the-art equipment and efficient workflow design.',
                    category: 'Restaurant',
                    client: 'Le Gourmet Restaurant',
                    location: 'Paris, France',
                    completionDate: 'March 2024',
                    projectUrl: '/projects/modern-restaurant-kitchen',
                    featured: true,
                },
                {
                    title: 'Luxury Hotel Suite',
                    description: 'Custom furniture and interior design for premium hotel suites with focus on comfort and elegance.',
                    category: 'Hotel',
                    client: 'Grand Hotel International',
                    location: 'Dubai, UAE',
                    completionDate: 'January 2024',
                    projectUrl: '/projects/luxury-hotel-suite',
                },
                {
                    title: 'Corporate Office Kitchen',
                    description: 'Modern office kitchen design for a tech company with collaborative spaces and premium appliances.',
                    category: 'Office',
                    client: 'TechCorp Solutions',
                    location: 'London, UK',
                    completionDate: 'December 2023',
                    projectUrl: '/projects/corporate-office-kitchen',
                },
            ],
        },
        {
            name: 'layout',
            type: 'group',
            label: 'Layout Settings',
            localized: true,
            fields: [
                {
                    name: 'columns',
                    type: 'select',
                    label: 'Grid Columns',
                    localized: true,
                    defaultValue: 'three',
                    options: [
                        { label: '2 Columns', value: 'two' },
                        { label: '3 Columns', value: 'three' },
                        { label: '4 Columns', value: 'four' },
                    ],
                    admin: {
                        description: 'Number of columns in the project grid',
                    },
                },
                {
                    name: 'showFilters',
                    type: 'checkbox',
                    label: 'Show Category Filters',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Display filter buttons to sort projects by category',
                    },
                },
                {
                    name: 'showFeatured',
                    type: 'checkbox',
                    label: 'Show Featured Section',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Display featured projects in a separate section',
                    },
                },
                {
                    name: 'itemsPerPage',
                    type: 'number',
                    label: 'Items Per Page',
                    localized: true,
                    defaultValue: 6,
                    min: 3,
                    max: 20,
                    admin: {
                        description: 'Number of projects to show initially (with load more button)',
                    },
                },
            ],
            admin: {
                description: 'Configure the layout and display options',
            },
        },
        {
            name: 'design',
            type: 'group',
            label: 'Design Settings',
            localized: true,
            fields: [
                {
                    name: 'backgroundColor',
                    type: 'select',
                    label: 'Background Color',
                    localized: true,
                    defaultValue: 'white',
                    options: [
                        { label: 'White', value: 'white' },
                        { label: 'Light Gray', value: 'light-gray' },
                        { label: 'Primary Light', value: 'primary-light' },
                        { label: 'Secondary Light', value: 'secondary-light' },
                    ],
                    admin: {
                        description: 'Background color for the section',
                    },
                },
                {
                    name: 'cardStyle',
                    type: 'select',
                    label: 'Card Style',
                    localized: true,
                    defaultValue: 'modern',
                    options: [
                        { label: 'Modern', value: 'modern' },
                        { label: 'Classic', value: 'classic' },
                        { label: 'Minimal', value: 'minimal' },
                        { label: 'Elevated', value: 'elevated' },
                    ],
                    admin: {
                        description: 'Visual style of the project cards',
                    },
                },
                {
                    name: 'hoverEffect',
                    type: 'select',
                    label: 'Hover Effect',
                    localized: true,
                    defaultValue: 'lift',
                    options: [
                        { label: 'Lift Up', value: 'lift' },
                        { label: 'Zoom In', value: 'zoom' },
                        { label: 'Fade', value: 'fade' },
                        { label: 'Slide', value: 'slide' },
                    ],
                    admin: {
                        description: 'Animation effect when hovering over cards',
                    },
                },
                {
                    name: 'showOverlay',
                    type: 'checkbox',
                    label: 'Show Image Overlay',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Display gradient overlay on hover for better text readability',
                    },
                },
                {
                    name: 'roundedCorners',
                    type: 'checkbox',
                    label: 'Rounded Corners',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Use rounded corners for cards',
                    },
                },
                {
                    name: 'showShadow',
                    type: 'checkbox',
                    label: 'Show Card Shadow',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Add subtle shadow to cards',
                    },
                },
            ],
            admin: {
                description: 'Customize the visual appearance of the project cards',
            },
        },
    ],
}

export default ProjectsShowcase
