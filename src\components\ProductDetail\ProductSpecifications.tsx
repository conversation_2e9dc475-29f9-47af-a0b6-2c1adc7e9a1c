'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, Ruler, Palette, Tag, Weight, Package, Zap } from 'lucide-react'
import { cn } from '@/utilities/ui'

interface ProductSpecificationsProps {
  dimensions?: {
    width?: number
    height?: number
    depth?: number
    weight?: number
  }
  materials?: Array<{
    material: string
    percentage?: number
  }>
  colors?: Array<{
    name: string
    hexCode?: string
    image?: any
  }>
  format?: string
  categories?: Array<{
    id: string
    title: string
    slug: string
  }>
  specifications?: Record<string, any>
  locale?: string
  className?: string
}

export const ProductSpecifications: React.FC<ProductSpecificationsProps> = ({
  dimensions,
  materials = [],
  colors = [],
  format,
  categories = [],
  specifications = {},
  locale = 'en',
  className
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['dimensions', 'materials']))

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const formatDimensions = (dims: typeof dimensions) => {
    if (!dims) return null
    const parts = []
    if (dims.width) parts.push(`${dims.width}cm ${locale === 'fr' ? 'L' : 'W'}`)
    if (dims.height) parts.push(`${dims.height}cm ${locale === 'fr' ? 'H' : 'H'}`)
    if (dims.depth) parts.push(`${dims.depth}cm ${locale === 'fr' ? 'P' : 'D'}`)
    return parts.join(' × ')
  }

  const getFormatDisplay = (format: string) => {
    const formatLabels = {
      'u-shaped': locale === 'fr' ? 'Cuisine en U' : 'U-shaped Kitchen',
      'l-shaped': locale === 'fr' ? 'Cuisine en L' : 'L-shaped Kitchen',
      'central-island': locale === 'fr' ? 'Cuisine avec îlot central' : 'Kitchen with Central Island'
    }
    return formatLabels[format as keyof typeof formatLabels] || format
  }

  const SpecificationSection = ({ 
    title, 
    icon: Icon, 
    sectionKey, 
    children, 
    iconColor = 'text-gray-500' 
  }: {
    title: string
    icon: any
    sectionKey: string
    children: React.ReactNode
    iconColor?: string
  }) => {
    const isExpanded = expandedSections.has(sectionKey)

    return (
      <div className="border border-gray-200 rounded-xl overflow-hidden">
        <button
          onClick={() => toggleSection(sectionKey)}
          className="w-full px-6 py-4 bg-gray-50 hover:bg-gray-100 transition-colors duration-200 flex items-center justify-between"
        >
          <div className="flex items-center gap-3">
            <Icon className={cn('w-5 h-5', iconColor)} />
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          </div>
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ChevronDown className="w-5 h-5 text-gray-500" />
          </motion.div>
        </button>
        
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="px-6 py-4 bg-white">
                {children}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }

  return (
    <div className={cn('space-y-4', className)}>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          {locale === 'fr' ? 'Spécifications' : 'Specifications'}
        </h2>
        <p className="text-gray-600">
          {locale === 'fr' 
            ? 'Détails techniques et caractéristiques du produit'
            : 'Technical details and product characteristics'
          }
        </p>
      </div>

      {/* Dimensions */}
      {dimensions && (
        <SpecificationSection
          title={locale === 'fr' ? 'Dimensions' : 'Dimensions'}
          icon={Ruler}
          sectionKey="dimensions"
          iconColor="text-blue-500"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {dimensions.width && (
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">{locale === 'fr' ? 'Largeur' : 'Width'}</span>
                <span className="font-semibold">{dimensions.width} cm</span>
              </div>
            )}
            {dimensions.height && (
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">{locale === 'fr' ? 'Hauteur' : 'Height'}</span>
                <span className="font-semibold">{dimensions.height} cm</span>
              </div>
            )}
            {dimensions.depth && (
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">{locale === 'fr' ? 'Profondeur' : 'Depth'}</span>
                <span className="font-semibold">{dimensions.depth} cm</span>
              </div>
            )}
            {dimensions.weight && (
              <div className="flex justify-between items-center py-2 border-b border-gray-100">
                <span className="text-gray-600">{locale === 'fr' ? 'Poids' : 'Weight'}</span>
                <span className="font-semibold">{dimensions.weight} kg</span>
              </div>
            )}
          </div>
          {formatDimensions(dimensions) && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>{locale === 'fr' ? 'Dimensions complètes:' : 'Full dimensions:'}</strong> {formatDimensions(dimensions)}
              </p>
            </div>
          )}
        </SpecificationSection>
      )}

      {/* Materials */}
      {materials.length > 0 && (
        <SpecificationSection
          title={locale === 'fr' ? 'Matériaux' : 'Materials'}
          icon={Tag}
          sectionKey="materials"
          iconColor="text-green-500"
        >
          <div className="space-y-3">
            {materials.map((material, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="font-medium text-gray-900">{material.material}</span>
                </div>
                {material.percentage && (
                  <span className="text-sm text-gray-600 bg-white px-2 py-1 rounded-full">
                    {material.percentage}%
                  </span>
                )}
              </div>
            ))}
          </div>
        </SpecificationSection>
      )}

      {/* Colors */}
      {colors.length > 0 && (
        <SpecificationSection
          title={locale === 'fr' ? 'Couleurs disponibles' : 'Available Colors'}
          icon={Palette}
          sectionKey="colors"
          iconColor="text-purple-500"
        >
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {colors.map((color, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                {color.hexCode && (
                  <div 
                    className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                    style={{ backgroundColor: color.hexCode }}
                  />
                )}
                <div>
                  <p className="font-medium text-gray-900">{color.name}</p>
                  {color.hexCode && (
                    <p className="text-xs text-gray-500 uppercase">{color.hexCode}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </SpecificationSection>
      )}

      {/* Format (for kitchen products) */}
      {format && (
        <SpecificationSection
          title={locale === 'fr' ? 'Format de cuisine' : 'Kitchen Format'}
          icon={Package}
          sectionKey="format"
          iconColor="text-orange-500"
        >
          <div className="p-4 bg-orange-50 rounded-lg">
            <div className="flex items-center gap-3 mb-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span className="font-semibold text-orange-900">{getFormatDisplay(format)}</span>
            </div>
            <p className="text-sm text-orange-800">
              {format === 'u-shaped' && (locale === 'fr' 
                ? 'Configuration en U maximisant l\'espace de rangement et de travail'
                : 'U-shaped configuration maximizing storage and workspace'
              )}
              {format === 'l-shaped' && (locale === 'fr'
                ? 'Configuration en L optimisant l\'utilisation de l\'espace'
                : 'L-shaped configuration optimizing space utilization'
              )}
              {format === 'central-island' && (locale === 'fr'
                ? 'Cuisine avec îlot central pour plus d\'espace de travail'
                : 'Kitchen with central island for additional workspace'
              )}
            </p>
          </div>
        </SpecificationSection>
      )}

      {/* Categories */}
      {categories.length > 0 && (
        <SpecificationSection
          title={locale === 'fr' ? 'Catégories' : 'Categories'}
          icon={Zap}
          sectionKey="categories"
          iconColor="text-red-500"
        >
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <span
                key={category.id}
                className="px-3 py-1.5 bg-red-100 text-red-800 rounded-full text-sm font-medium"
              >
                {category.title}
              </span>
            ))}
          </div>
        </SpecificationSection>
      )}

      {/* Additional Specifications */}
      {Object.keys(specifications).length > 0 && (
        <SpecificationSection
          title={locale === 'fr' ? 'Spécifications additionnelles' : 'Additional Specifications'}
          icon={Weight}
          sectionKey="additional"
          iconColor="text-gray-500"
        >
          <div className="space-y-2">
            {Object.entries(specifications).map(([key, value]) => (
              <div key={key} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                <span className="font-semibold">{String(value)}</span>
              </div>
            ))}
          </div>
        </SpecificationSection>
      )}
    </div>
  )
}
