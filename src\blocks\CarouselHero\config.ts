import type { Block } from 'payload'

export const CarouselHero: Block = {
    slug: 'carouselHero',
    interfaceName: 'CarouselHeroBlock',
    labels: {
        singular: 'Carousel Hero',
        plural: 'Carousel Hero Blocks',
    },
    fields: [
        {
            name: 'slides',
            type: 'array',
            label: 'Slides',
            localized: true,
            labels: {
                singular: 'Slide',
                plural: 'Slides',
            },
            fields: [
                {
                    name: 'image',
                    type: 'upload',
                    label: 'Background Image',
                    localized: true,
                    relationTo: 'media',
                    required: true,
                    admin: {
                        description: 'Full-screen background image for this slide (recommended: 1920x1080 or higher)',
                    },
                },
                {
                    name: 'title',
                    type: 'text',
                    label: 'Title',
                    localized: true,
                    admin: {
                        description: 'Main heading for this slide',
                    },
                },
                {
                    name: 'subtitle',
                    type: 'text',
                    label: 'Subtitle',
                    localized: true,
                    admin: {
                        description: 'Secondary heading or tagline',
                    },
                },
                {
                    name: 'description',
                    type: 'textarea',
                    label: 'Description',
                    localized: true,
                    admin: {
                        description: 'Additional descriptive text',
                    },
                },
                {
                    name: 'cta<PERSON><PERSON>on',
                    type: 'group',
                    label: 'Call to Action Button',
                    localized: true,
                    fields: [
                        {
                            name: 'label',
                            type: 'text',
                            label: 'Button Text',
                            localized: true,
                        },
                        {
                            name: 'link',
                            type: 'text',
                            label: 'Button Link',
                            localized: true,
                            admin: {
                                description: 'URL or path for the button (e.g., /contact, https://example.com)',
                            },
                        },
                    ],
                },
            ],
            admin: {
                initCollapsed: false,
                description: 'Add multiple slides to create a carousel effect',
            },
            minRows: 1,
            defaultValue: [
                {
                    title: 'Welcome to Our Website',
                    subtitle: 'Discover Amazing Experiences',
                    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.',
                },
                {
                    title: 'Innovation at Its Best',
                    subtitle: 'Leading the Future',
                    description: 'Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
                },
                {
                    title: 'Join Our Community',
                    subtitle: 'Be Part of Something Great',
                    description: 'Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.',
                },
            ],
        },
        {
            name: 'settings',
            type: 'group',
            label: 'Carousel Settings',
            localized: true,
            fields: [
                {
                    name: 'autoPlay',
                    type: 'checkbox',
                    label: 'Auto Play',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Automatically advance slides',
                    },
                },
                {
                    name: 'autoPlayInterval',
                    type: 'number',
                    label: 'Auto Play Interval (ms)',
                    localized: true,
                    defaultValue: 5000,
                    min: 1000,
                    max: 30000,
                    admin: {
                        description: 'Time between slide transitions in milliseconds (1000 = 1 second)',
                        condition: (data, siblingData) => siblingData?.autoPlay === true,
                    },
                },
                {
                    name: 'showArrows',
                    type: 'checkbox',
                    label: 'Show Navigation Arrows',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Display left/right arrow buttons',
                    },
                },
                {
                    name: 'showDots',
                    type: 'checkbox',
                    label: 'Show Dot Indicators',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Display dot indicators at the bottom',
                    },
                },
                {
                    name: 'transitionDuration',
                    type: 'number',
                    label: 'Transition Duration (ms)',
                    localized: true,
                    defaultValue: 1000,
                    min: 300,
                    max: 3000,
                    admin: {
                        description: 'Duration of slide transition animation',
                    },
                },
            ],
            admin: {
                description: 'Configure carousel behavior and controls',
            },
        },
        {
            name: 'design',
            type: 'group',
            label: 'Design Settings',
            localized: true,
            fields: [
                {
                    name: 'height',
                    type: 'select',
                    label: 'Hero Height',
                    localized: true,
                    defaultValue: 'full',
                    options: [
                        { label: 'Small (60vh)', value: 'small' },
                        { label: 'Medium (80vh)', value: 'medium' },
                        { label: 'Large (90vh)', value: 'large' },
                        { label: 'Full Screen (100vh)', value: 'full' },
                    ],
                    admin: {
                        description: 'Height of the hero section',
                    },
                },
                {
                    name: 'overlayOpacity',
                    type: 'select',
                    label: 'Overlay Opacity',
                    localized: true,
                    defaultValue: 'medium',
                    options: [
                        { label: 'None', value: 'none' },
                        { label: 'Light (20%)', value: 'light' },
                        { label: 'Medium (40%)', value: 'medium' },
                        { label: 'Heavy (60%)', value: 'heavy' },
                    ],
                    admin: {
                        description: 'Dark overlay opacity for better text readability',
                    },
                },
                {
                    name: 'textPosition',
                    type: 'select',
                    label: 'Text Position',
                    localized: true,
                    defaultValue: 'center',
                    options: [
                        { label: 'Center', value: 'center' },
                        { label: 'Left', value: 'left' },
                        { label: 'Right', value: 'right' },
                        { label: 'Bottom Left', value: 'bottom-left' },
                        { label: 'Bottom Center', value: 'bottom-center' },
                        { label: 'Bottom Right', value: 'bottom-right' },
                    ],
                    admin: {
                        description: 'Position of the text content on the slide',
                    },
                },
                {
                    name: 'textColor',
                    type: 'select',
                    label: 'Text Color',
                    localized: true,
                    defaultValue: 'white',
                    options: [
                        { label: 'White', value: 'white' },
                        { label: 'Dark', value: 'dark' },
                        { label: 'Auto', value: 'auto' },
                    ],
                    admin: {
                        description: 'Color of the text content',
                    },
                },
            ],
            admin: {
                description: 'Customize the visual appearance of the hero',
            },
        },
    ],
}

export default CarouselHero
