import type { CollectionConfig } from 'payload'

import {
  BlocksFeature,
  FixedToolbarFeature,
  HeadingFeature,
  HorizontalRuleFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

import { authenticated } from '../../access/authenticated'
import { authenticatedOrPublished } from '../../access/authenticatedOrPublished'
import { Banner } from '../../blocks/Banner/config'
import { Code } from '../../blocks/Code/config'
import { MediaBlock } from '../../blocks/MediaBlock/config'
import { Table } from '../../blocks/Table/config'
import { Timeline } from '../../blocks/Timeline/config'
import { FAQAccordion } from '../../blocks/FAQAccordion/config'
import { Tabs } from '../../blocks/Tabs/config'
import { BeforeAfter } from '../../blocks/BeforeAfter/config'
import { CertificationsAlternate } from '../../blocks/CertificationsAlternate/config'
import { StatsCounter } from '../../blocks/StatsCounter/config'
import { TeamBio } from '../../blocks/TeamBio/config'
import { LayoutGrid } from '../../blocks/LayoutGrid/config'
import { generatePreviewPath } from '../../utilities/generatePreviewPath'
import { populateAuthors } from './hooks/populateAuthors'
import { revalidateDelete, revalidateProduct } from './hooks/revalidateProduct'

import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'
import { slugField } from '@/fields/slug'

export const Products: CollectionConfig<'products'> = {
  slug: 'products',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  // This config controls what's populated by default when a product is referenced
  defaultPopulate: {
    title: true,
    slug: true,
    categories: true,
    format: true,
    heroImage: true,
    meta: {
      image: true,
      description: true,
    },
  },
  admin: {
    defaultColumns: ['title', 'categories', 'format', 'slug', 'updatedAt'],
    group: 'Products',
    livePreview: {
      url: ({ data, req }) => {
        const path = generatePreviewPath({
          slug: typeof data?.slug === 'string' ? data.slug : '',
          collection: 'products',
          req,
        })

        return path
      },
    },
    preview: (data, { req }) =>
      generatePreviewPath({
        slug: typeof data?.slug === 'string' ? data.slug : '',
        collection: 'products',
        req,
      }),
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      localized: true,
      admin: {
        description: 'Product name/title',
      },
    },
    {
      type: 'tabs',
      tabs: [
        {
          fields: [
            {
              name: 'heroImage',
              type: 'upload',
              relationTo: 'media',
              admin: {
                description: 'Main product image',
              },
            },
            {
              name: 'gallery',
              type: 'array',
              admin: {
                description: 'Additional product images',
              },
              fields: [
                {
                  name: 'image',
                  type: 'upload',
                  relationTo: 'media',
                  required: true,
                },
                {
                  name: 'caption',
                  type: 'text',
                  admin: {
                    description: 'Optional caption for this image',
                  },
                },
              ],
            },
            {
              name: 'description',
              type: 'richText',
              localized: true,
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
                    BlocksFeature({
                      blocks: [
                        Banner,
                        Code,
                        MediaBlock,
                        Table,
                        Timeline,
                        FAQAccordion,
                        Tabs,
                        BeforeAfter,
                        CertificationsAlternate,
                        StatsCounter,
                        TeamBio,
                        LayoutGrid,
                      ],
                    }),
                    FixedToolbarFeature(),
                    InlineToolbarFeature(),
                    HorizontalRuleFeature(),
                  ]
                },
              }),
              label: 'Product Description',
              required: true,
              admin: {
                description: 'Detailed product description with rich content',
              },
            },
          ],
          label: 'Content',
        },
        {
          fields: [
            {
              name: 'format',
              type: 'select',
              localized: true,
              admin: {
                description: 'Product format/style (conditional based on category)',
                condition: (data, siblingData) => {
                  // Show format field only for specific categories
                  const categories = data?.categories || siblingData?.categories;
                  if (!categories || !Array.isArray(categories)) return false;

                  // Check if any category is "Cuisines" (kitchen category)
                  return categories.some(cat => {
                    if (typeof cat === 'object' && cat !== null) {
                      return cat.slug === 'cuisines' || cat.title === 'Cuisines';
                    }
                    return false;
                  });
                },
              },
              options: [
                {
                  label: {
                    en: 'U-shaped Kitchen',
                    fr: 'Cuisine en U',
                  },
                  value: 'u-shaped',
                },
                {
                  label: {
                    en: 'L-shaped Kitchen',
                    fr: 'Cuisine en L',
                  },
                  value: 'l-shaped',
                },
                {
                  label: {
                    en: 'Kitchen with Central Island',
                    fr: 'Cuisine avec îlot central',
                  },
                  value: 'central-island',
                },
              ],
            },
            {
              name: 'dimensions',
              type: 'group',
              admin: {
                description: 'Product dimensions',
              },
              fields: [
                {
                  type: 'row',
                  fields: [
                    {
                      name: 'width',
                      type: 'number',
                      admin: {
                        width: '33%',
                        description: 'Width in cm',
                      },
                    },
                    {
                      name: 'height',
                      type: 'number',
                      admin: {
                        width: '33%',
                        description: 'Height in cm',
                      },
                    },
                    {
                      name: 'depth',
                      type: 'number',
                      admin: {
                        width: '33%',
                        description: 'Depth in cm',
                      },
                    },
                  ],
                },
                {
                  name: 'weight',
                  type: 'number',
                  admin: {
                    description: 'Weight in kg',
                  },
                },
              ],
            },
            {
              name: 'materials',
              type: 'array',
              localized: true,
              admin: {
                description: 'Materials used in this product',
              },
              fields: [
                {
                  name: 'material',
                  type: 'text',
                  required: true,
                  admin: {
                    description: 'Material name (e.g., Oak Wood, Stainless Steel)',
                  },
                },
                {
                  name: 'percentage',
                  type: 'number',
                  admin: {
                    description: 'Percentage of this material (optional)',
                    step: 0.1,
                  },
                },
              ],
            },
            {
              name: 'colors',
              type: 'array',
              localized: true,
              admin: {
                description: 'Available colors for this product',
              },
              fields: [
                {
                  name: 'name',
                  type: 'text',
                  required: true,
                  admin: {
                    description: 'Color name (e.g., Natural Oak, Matte Black)',
                  },
                },
                {
                  name: 'hexCode',
                  type: 'text',
                  admin: {
                    description: 'Hex color code (e.g., #8B4513)',
                  },
                },
                {
                  name: 'image',
                  type: 'upload',
                  relationTo: 'media',
                  admin: {
                    description: 'Image showing this color variant',
                  },
                },
              ],
            },
          ],
          label: 'Product Details',
        },
        {
          fields: [
            {
              name: 'relatedProducts',
              type: 'relationship',
              admin: {
                position: 'sidebar',
                description: 'Related or similar products',
              },
              filterOptions: ({ id }) => {
                return {
                  id: {
                    not_in: [id],
                  },
                }
              },
              hasMany: true,
              relationTo: 'products',
            },
            {
              name: 'categories',
              type: 'relationship',
              admin: {
                position: 'sidebar',
                description: 'Product categories',
              },
              hasMany: true,
              relationTo: 'product-categories',
              required: true,
            },
          ],
          label: 'Relationships',
        },
        {
          name: 'meta',
          label: 'SEO',
          fields: [
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image',
            }),
            MetaTitleField({
              hasGenerateFn: true,
            }),
            MetaImageField({
              relationTo: 'media',
            }),
            MetaDescriptionField({}),
            PreviewField({
              hasGenerateFn: true,
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
            }),
          ],
        },
      ],
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        position: 'sidebar',
      },
      hooks: {
        beforeChange: [
          ({ siblingData, value }) => {
            if (siblingData._status === 'published' && !value) {
              return new Date()
            }
            return value
          },
        ],
      },
    },
    {
      name: 'authors',
      type: 'relationship',
      admin: {
        position: 'sidebar',
        description: 'Product creators/designers',
      },
      hasMany: true,
      relationTo: 'users',
    },
    // This field is only used to populate the user data via the `populateAuthors` hook
    {
      name: 'populatedAuthors',
      type: 'array',
      access: {
        update: () => false,
      },
      admin: {
        disabled: true,
        readOnly: true,
      },
      fields: [
        {
          name: 'id',
          type: 'text',
        },
        {
          name: 'name',
          type: 'text',
        },
      ],
    },
    ...slugField(),
  ],
  hooks: {
    afterChange: [revalidateProduct],
    afterRead: [populateAuthors],
    afterDelete: [revalidateDelete],
  },
  versions: {
    drafts: {
      autosave: {
        interval: 100,
      },
      schedulePublish: true,
    },
    maxPerDoc: 50,
  },
}
