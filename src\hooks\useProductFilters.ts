'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams, usePathname } from 'next/navigation'

export interface ProductFilterState {
  category: string | null
  format: string | null
}

export interface ProductFilterActions {
  setCategory: (category: string | null) => void
  setFormat: (format: string | null) => void
  clearFilters: () => void
  clearCategory: () => void
  clearFormat: () => void
}

export interface ProductFilterHookReturn {
  filters: ProductFilterState
  actions: ProductFilterActions
  isLoading: boolean
  hasActiveFilters: boolean
}

interface UseProductFiltersOptions {
  /**
   * Whether to sync filter state with URL parameters
   * @default true
   */
  syncWithURL?: boolean
  
  /**
   * Whether to show loading state during filter transitions
   * @default true
   */
  showLoadingState?: boolean
  
  /**
   * Loading delay in milliseconds for better UX
   * @default 300
   */
  loadingDelay?: number
  
  /**
   * Whether to scroll to top when filters change
   * @default false
   */
  scrollToTop?: boolean
  
  /**
   * Callback fired when filters change
   */
  onFiltersChange?: (filters: ProductFilterState) => void
}

/**
 * Custom hook for managing product filter state with URL synchronization
 * Provides type-safe filter management with bookmarking support
 */
export function useProductFilters(options: UseProductFiltersOptions = {}): ProductFilterHookReturn {
  const {
    syncWithURL = true,
    showLoadingState = true,
    loadingDelay = 300,
    scrollToTop = false,
    onFiltersChange
  } = options

  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  
  // Initialize filter state from URL parameters
  const [filters, setFilters] = useState<ProductFilterState>(() => ({
    category: syncWithURL ? searchParams.get('category') : null,
    format: syncWithURL ? searchParams.get('format') : null,
  }))
  
  const [isLoading, setIsLoading] = useState(false)

  // Check if any filters are active
  const hasActiveFilters = Boolean(filters.category || filters.format)

  // Update URL when filters change
  const updateURL = useCallback((newFilters: ProductFilterState) => {
    if (!syncWithURL) return

    const params = new URLSearchParams()
    
    if (newFilters.category) {
      params.set('category', newFilters.category)
    }
    if (newFilters.format) {
      params.set('format', newFilters.format)
    }
    
    const queryString = params.toString()
    const newURL = queryString ? `${pathname}?${queryString}` : pathname
    
    router.replace(newURL, { scroll: scrollToTop })
  }, [pathname, router, syncWithURL, scrollToTop])

  // Generic filter update function
  const updateFilters = useCallback((updates: Partial<ProductFilterState>) => {
    const newFilters = { ...filters, ...updates }
    
    // Auto-clear format when switching away from kitchen category
    if (updates.category !== undefined && updates.category !== 'cuisines') {
      newFilters.format = null
    }
    
    setFilters(newFilters)
    updateURL(newFilters)
    
    // Show loading state
    if (showLoadingState) {
      setIsLoading(true)
      setTimeout(() => setIsLoading(false), loadingDelay)
    }
    
    // Fire callback
    if (onFiltersChange) {
      onFiltersChange(newFilters)
    }
  }, [filters, updateURL, showLoadingState, loadingDelay, onFiltersChange])

  // Filter actions
  const actions: ProductFilterActions = {
    setCategory: useCallback((category: string | null) => {
      updateFilters({ category })
    }, [updateFilters]),

    setFormat: useCallback((format: string | null) => {
      updateFilters({ format })
    }, [updateFilters]),

    clearFilters: useCallback(() => {
      updateFilters({ category: null, format: null })
    }, [updateFilters]),

    clearCategory: useCallback(() => {
      updateFilters({ category: null })
    }, [updateFilters]),

    clearFormat: useCallback(() => {
      updateFilters({ format: null })
    }, [updateFilters]),
  }

  // Sync with URL changes (browser back/forward)
  useEffect(() => {
    if (!syncWithURL) return

    const category = searchParams.get('category')
    const format = searchParams.get('format')
    
    const newFilters = { category, format }
    
    // Only update if different from current state
    if (newFilters.category !== filters.category || newFilters.format !== filters.format) {
      setFilters(newFilters)
      
      if (onFiltersChange) {
        onFiltersChange(newFilters)
      }
    }
  }, [searchParams, syncWithURL, filters.category, filters.format, onFiltersChange])

  return {
    filters,
    actions,
    isLoading,
    hasActiveFilters,
  }
}

/**
 * Utility function to create filter query parameters for API calls
 */
export function createFilterQuery(filters: ProductFilterState): Record<string, any> {
  const query: Record<string, any> = {}
  
  if (filters.category) {
    query['categories.slug'] = {
      equals: filters.category
    }
  }
  
  if (filters.format) {
    query.format = {
      equals: filters.format
    }
  }
  
  return Object.keys(query).length > 0 ? { where: query } : {}
}

/**
 * Utility function to get filter display names
 */
export function getFilterDisplayNames(
  filters: ProductFilterState,
  categories: Array<{ slug: string; title: string }>,
  formatOptions: Array<{ value: string; label: string }>,
  locale: string = 'en'
): { category?: string; format?: string } {
  const displayNames: { category?: string; format?: string } = {}
  
  if (filters.category) {
    const category = categories.find(cat => cat.slug === filters.category)
    displayNames.category = category?.title || filters.category
  }
  
  if (filters.format) {
    const format = formatOptions.find(opt => opt.value === filters.format)
    displayNames.format = format?.label || filters.format
  }
  
  return displayNames
}

/**
 * Type guard to check if filters are valid
 */
export function isValidFilterState(filters: any): filters is ProductFilterState {
  return (
    typeof filters === 'object' &&
    filters !== null &&
    (filters.category === null || typeof filters.category === 'string') &&
    (filters.format === null || typeof filters.format === 'string')
  )
}

/**
 * Utility to serialize filters for storage/transmission
 */
export function serializeFilters(filters: ProductFilterState): string {
  return JSON.stringify(filters)
}

/**
 * Utility to deserialize filters from storage/transmission
 */
export function deserializeFilters(serialized: string): ProductFilterState | null {
  try {
    const parsed = JSON.parse(serialized)
    return isValidFilterState(parsed) ? parsed : null
  } catch {
    return null
  }
}
