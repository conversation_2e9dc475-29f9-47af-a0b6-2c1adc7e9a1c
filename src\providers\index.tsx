'use client'

import React from 'react'

import { AdminBarProvider } from './AdminBarProvider'
import { HeaderThemeProvider } from './HeaderTheme'
import { ThemeProvider } from './Theme/index'
import { FontSizeProvider } from '@/providers/FontSize'
import { FontSettingsSynchronizer } from '@/components/FontSettingsSynchronizer'

export const Providers: React.FC<{
  children: React.ReactNode
}> = ({ children }) => {
  // Default font settings
  const fontSettings = {
    showFontControls: true,
    defaultFontScale: '1',
  }

  return (
    <>
      {/* Add FontSettingsSynchronizer here instead of in layout */}
      <FontSettingsSynchronizer
        showFontControls={fontSettings.showFontControls}
        defaultFontScale={fontSettings.defaultFontScale}
      />

      <ThemeProvider>
        <FontSizeProvider>
          <HeaderThemeProvider>
            <AdminBarProvider>{children}</AdminBarProvider>
          </HeaderThemeProvider>
        </FontSizeProvider>
      </ThemeProvider>
    </>
  )
}
