import { Block } from 'payload'
import { lexicalEditor } from '@payloadcms/richtext-lexical'

export const LayoutGrid: Block = {
  slug: 'LayoutGrid',
  labels: {
    singular: 'Layout Grid',
    plural: 'Layout Grid Blocks',
  },
  fields: [
    {
      name: 'heading',
      label: 'Heading',
      type: 'text',
      localized: true,
      admin: {
        description: 'Optional heading for the grid',
      },
    },
    {
      name: 'subheading',
      label: 'Subheading',
      type: 'text',
      localized: true,
      admin: {
        description: 'Optional subheading or description',
      },
    },
    {
      type: 'row',
      fields: [
        {
          name: 'layout',
          label: 'Layout Style',
          type: 'select',
          localized: true,
          defaultValue: 'asymmetric',
          options: [
            { label: 'Asymmetric (Like Reference)', value: 'asymmetric' },
            { label: 'Uniform Grid', value: 'uniform' },
          ],
          admin: {
            description: 'Choose between asymmetric layout (like reference image) or uniform grid',
            width: '50%',
          },
        },
        {
          name: 'gridGap',
          label: 'Grid Gap',
          type: 'select',
          localized: true,
          defaultValue: 'medium',
          options: [
            { label: 'Small', value: 'small' },
            { label: 'Medium', value: 'medium' },
            { label: 'Large', value: 'large' },
          ],
          admin: {
            description: 'Spacing between grid items',
            width: '50%',
          },
        },
      ],
    },
    {
      name: 'hoverEffect',
      label: 'Hover Effect',
      type: 'select',
      localized: true,
      defaultValue: 'zoom',
      options: [
        { label: 'Zoom', value: 'zoom' },
        { label: 'Glow', value: 'glow' },
        { label: 'Border', value: 'border' },
        { label: 'None', value: 'none' },
      ],
      admin: {
        description: 'Effect when hovering over grid items',
      },
    },
    {
      type: 'row',
      fields: [
        {
          name: 'backgroundColor',
          label: 'Background Color',
          type: 'select',
          localized: true,
          defaultValue: 'bg-background',
          options: [
            { label: 'Default', value: 'bg-background' },
            { label: 'Primary', value: 'bg-primary' },
            { label: 'Secondary', value: 'bg-secondary' },
            { label: 'Accent', value: 'bg-accent' },
            { label: 'Muted', value: 'bg-muted' },
            { label: 'Card', value: 'bg-card' },
          ],
          admin: {
            description: 'Background color for the entire section',
            width: '50%',
          },
        },
        {
          name: 'textColor',
          label: 'Text Color',
          type: 'select',
          localized: true,
          defaultValue: 'text-foreground',
          options: [
            { label: 'Default', value: 'text-foreground' },
            { label: 'Primary', value: 'text-primary' },
            { label: 'Secondary', value: 'text-secondary' },
            { label: 'Accent', value: 'text-accent' },
            { label: 'Muted', value: 'text-muted-foreground' },
            { label: 'Card', value: 'text-card-foreground' },
            { label: 'White', value: 'text-white' },
          ],
          admin: {
            description: 'Text color for the entire section',
            width: '50%',
          },
        },
      ],
    },
    {
      name: 'items',
      label: 'Grid Items',
      type: 'array',
      localized: true,
      minRows: 1,
      maxRows: 12,
      admin: {
        description: 'Add items to the grid (up to 12)',
      },
      fields: [
        {
          name: 'id',
          type: 'text',
          localized: true,
          admin: {
            hidden: true,
          },
          hooks: {
            beforeValidate: [
              ({ data }) => {
                return `grid-item-${Math.random().toString(36).substr(2, 9)}`
              },
            ],
          },
        },
        {
          name: 'gridArea',
          label: 'Grid Item Size',
          type: 'select',
          localized: true,
          options: [
            { label: 'Auto (Based on Layout)', value: '' },
            { label: 'Main (Large 2x2)', value: 'main' },
            { label: 'Tall (1x2)', value: 'tall' },
            { label: 'Wide (2x1)', value: 'wide' },
            { label: 'Small (1x1)', value: 'small' },
          ],
          admin: {
            description: 'Size and position of this item in the grid (for asymmetric layout)',
          },
        },
        {
          name: 'title',
          label: 'Title',
          type: 'text',
          localized: true,
        },
        {
          name: 'description',
          label: 'Description',
          type: 'richText',
          localized: true,
          editor: lexicalEditor(),
        },
        {
          name: 'media',
          label: 'Media',
          type: 'upload',
          relationTo: 'media',
          localized: true,
          required: true,
          admin: {
            description: 'Image for this grid item (required)',
          },
        },
        {
          name: 'link',
          label: 'Link',
          type: 'group',
          localized: true,
          admin: {
            description: 'Optional link for this item',
          },
          fields: [
            {
              name: 'type',
              label: 'Link Type',
              type: 'radio',
              localized: true,
              options: [
                {
                  label: 'Custom URL',
                  value: 'custom',
                },
                {
                  label: 'Internal Page',
                  value: 'reference',
                },
              ],
              defaultValue: 'custom',
            },
            {
              name: 'label',
              label: 'Label',
              type: 'text',
              localized: true,
              required: false,
              admin: {
                condition: (_, siblingData) =>
                  siblingData?.type === 'custom' || siblingData?.type === 'reference',
              },
            },
            {
              name: 'url',
              label: 'URL',
              type: 'text',
              localized: true,
              required: false,
              admin: {
                condition: (_, siblingData) => siblingData?.type === 'custom',
              },
            },
            {
              name: 'reference',
              label: 'Page to link to',
              type: 'relationship',
              relationTo: ['pages', 'posts'],
              localized: true,
              required: false,
              admin: {
                condition: (_, siblingData) => siblingData?.type === 'reference',
              },
            },
            {
              name: 'newTab',
              label: 'Open in new tab',
              type: 'checkbox',
              localized: true,
            },
          ],
        },
        {
          type: 'row',
          fields: [
            {
              name: 'backgroundColor',
              label: 'Background Color',
              type: 'select',
              localized: true,
              options: [
                { label: 'Default', value: '' },
                { label: 'Primary', value: 'bg-primary' },
                { label: 'Secondary', value: 'bg-secondary' },
                { label: 'Accent', value: 'bg-accent' },
                { label: 'Muted', value: 'bg-muted' },
                { label: 'Card', value: 'bg-card' },
              ],
              admin: {
                width: '50%',
              },
            },
            {
              name: 'textColor',
              label: 'Text Color',
              type: 'select',
              localized: true,
              options: [
                { label: 'Default', value: '' },
                { label: 'Primary', value: 'text-primary' },
                { label: 'Secondary', value: 'text-secondary' },
                { label: 'Accent', value: 'text-accent' },
                { label: 'Muted', value: 'text-muted-foreground' },
                { label: 'Card', value: 'text-card-foreground' },
                { label: 'White', value: 'text-white' },
              ],
              admin: {
                width: '50%',
              },
            },
          ],
        },
      ],
    },
  ],
}
