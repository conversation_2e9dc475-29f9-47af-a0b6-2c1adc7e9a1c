'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Filter } from 'lucide-react'
import { CategoryFilter, ProductCategory } from './CategoryFilter'
import { FormatFilter, FormatOption } from './FormatFilter'
import { cn } from '@/utilities/ui'

interface ProductFiltersProps {
  categories: ProductCategory[]
  selectedCategory: string | null
  selectedFormat: string | null
  onCategoryChange: (categorySlug: string | null) => void
  onFormatChange: (formatValue: string | null) => void
  onClearFilters: () => void
  className?: string
  locale?: string
  isLoading?: boolean
}

// Kitchen format options based on the design specifications
const KITCHEN_FORMATS: Record<string, FormatOption[]> = {
  en: [
    { label: 'U-shaped Kitchen', value: 'u-shaped' },
    { label: 'L-shaped Kitchen', value: 'l-shaped' },
    { label: 'Kitchen with Central Island', value: 'central-island' },
  ],
  fr: [
    { label: 'Cuisine en U', value: 'u-shaped' },
    { label: 'Cuisine en L', value: 'l-shaped' },
    { label: 'Cuisine avec îlot central', value: 'central-island' },
  ],
}

export const ProductFilters: React.FC<ProductFiltersProps> = ({
  categories,
  selectedCategory,
  selectedFormat,
  onCategoryChange,
  onFormatChange,
  onClearFilters,
  className,
  locale = 'en',
  isLoading = false
}) => {
  const [isMobileFiltersOpen, setIsMobileFiltersOpen] = useState(false)

  // Determine if format filter should be visible
  const selectedCategoryData = selectedCategory
    ? categories.find(cat => cat.slug === selectedCategory)
    : null

  const showFormatFilter = selectedCategoryData?.slug === 'cuisines'
  const formatOptions = KITCHEN_FORMATS[locale] || KITCHEN_FORMATS.en

  // Check if any filters are active
  const hasActiveFilters = selectedCategory || selectedFormat

  // Clear format when category changes to non-kitchen category
  useEffect(() => {
    if (!showFormatFilter && selectedFormat) {
      onFormatChange(null)
    }
  }, [showFormatFilter, selectedFormat, onFormatChange])

  return (
    <div className={cn('w-full', className)}>
      {/* Desktop Filters */}
      <div className="hidden md:block">
        <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Filter className="w-5 h-5 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-900">
                {locale === 'fr' ? 'Filtrer les produits' : 'Filter Products'}
              </h3>
            </div>

            {hasActiveFilters && (
              <button
                onClick={onClearFilters}
                className="flex items-center gap-2 px-3 py-1.5 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200"
              >
                <X className="w-4 h-4" />
                {locale === 'fr' ? 'Effacer' : 'Clear'}
              </button>
            )}
          </div>

          {/* Filter Controls */}
          <div className="space-y-4">
            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {locale === 'fr' ? 'Catégorie' : 'Category'}
              </label>
              <CategoryFilter
                categories={categories}
                selectedCategory={selectedCategory}
                onCategoryChange={onCategoryChange}
                locale={locale}
              />
            </div>

            {/* Format Filter */}
            <AnimatePresence>
              {showFormatFilter && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {locale === 'fr' ? 'Format de cuisine' : 'Kitchen Format'}
                  </label>
                  <FormatFilter
                    formats={formatOptions}
                    selectedFormat={selectedFormat}
                    onFormatChange={onFormatChange}
                    locale={locale}
                    isVisible={showFormatFilter}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Active Filters Summary */}
          <AnimatePresence>
            {hasActiveFilters && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                className="mt-6 pt-4 border-t border-gray-100"
              >
                <div className="mb-3">
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    {locale === 'fr' ? 'Filtres actifs' : 'Active Filters'}
                  </span>
                </div>
                <div className="flex flex-wrap gap-2">
                  <AnimatePresence>
                    {selectedCategory && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                        className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-red-100 to-red-50 text-red-800 rounded-full text-sm border border-red-200 shadow-sm"
                      >
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="font-medium">{categories.find(cat => cat.slug === selectedCategory)?.title}</span>
                        <button
                          onClick={() => onCategoryChange(null)}
                          className="hover:bg-red-200 rounded-full p-1 transition-all duration-150 hover:scale-110"
                          aria-label={locale === 'fr' ? 'Supprimer le filtre de catégorie' : 'Remove category filter'}
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </motion.div>
                    )}
                    {selectedFormat && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.2, delay: 0.1 }}
                        className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-orange-100 to-orange-50 text-orange-800 rounded-full text-sm border border-orange-200 shadow-sm"
                      >
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="font-medium">{formatOptions.find(f => f.value === selectedFormat)?.label}</span>
                        <button
                          onClick={() => onFormatChange(null)}
                          className="hover:bg-orange-200 rounded-full p-1 transition-all duration-150 hover:scale-110"
                          aria-label={locale === 'fr' ? 'Supprimer le filtre de format' : 'Remove format filter'}
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Mobile Filters */}
      <div className="md:hidden">
        {/* Mobile Filter Button */}
        <motion.button
          onClick={() => setIsMobileFiltersOpen(true)}
          whileTap={{ scale: 0.98 }}
          className="relative flex items-center justify-center gap-2 w-full px-4 py-3 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg shadow-lg hover:shadow-xl hover:from-red-600 hover:to-red-700 transition-all duration-200"
        >
          <Filter className="w-5 h-5" />
          <span className="font-medium">
            {locale === 'fr' ? 'Filtres' : 'Filters'}
          </span>

          {/* Active Filter Count Badge */}
          <AnimatePresence>
            {hasActiveFilters && (
              <motion.span
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0, opacity: 0 }}
                transition={{ type: 'spring', stiffness: 500, damping: 30 }}
                className="absolute -top-2 -right-2 flex items-center justify-center w-6 h-6 bg-orange-500 text-white text-xs font-bold rounded-full shadow-lg border-2 border-white"
              >
                {(selectedCategory ? 1 : 0) + (selectedFormat ? 1 : 0)}
              </motion.span>
            )}
          </AnimatePresence>

          {/* Loading Indicator */}
          {isLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-red-600/80 rounded-lg">
              <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </motion.button>

        {/* Mobile Filter Modal */}
        <AnimatePresence>
          {isMobileFiltersOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 z-50"
                onClick={() => setIsMobileFiltersOpen(false)}
              />

              {/* Modal */}
              <motion.div
                initial={{ opacity: 0, y: '100%' }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: '100%' }}
                transition={{ type: 'spring', damping: 25, stiffness: 300 }}
                className="fixed bottom-0 left-0 right-0 bg-white rounded-t-2xl shadow-2xl z-50 max-h-[80vh] overflow-hidden"
              >
                {/* Modal Header */}
                <div className="flex items-center justify-between p-6 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {locale === 'fr' ? 'Filtrer les produits' : 'Filter Products'}
                  </h3>
                  <button
                    onClick={() => setIsMobileFiltersOpen(false)}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                  >
                    <X className="w-5 h-5 text-gray-500" />
                  </button>
                </div>

                {/* Modal Content */}
                <div className="p-6 overflow-y-auto">
                  <div className="space-y-6">
                    {/* Category Filter */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        {locale === 'fr' ? 'Catégorie' : 'Category'}
                      </label>
                      <CategoryFilter
                        categories={categories}
                        selectedCategory={selectedCategory}
                        onCategoryChange={onCategoryChange}
                        locale={locale}
                      />
                    </div>

                    {/* Format Filter */}
                    <AnimatePresence>
                      {showFormatFilter && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: 'auto' }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                        >
                          <label className="block text-sm font-medium text-gray-700 mb-3">
                            {locale === 'fr' ? 'Format de cuisine' : 'Kitchen Format'}
                          </label>
                          <FormatFilter
                            formats={formatOptions}
                            selectedFormat={selectedFormat}
                            onFormatChange={onFormatChange}
                            locale={locale}
                            isVisible={showFormatFilter}
                          />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>

                {/* Modal Footer */}
                <div className="p-6 border-t border-gray-200 bg-gray-50">
                  <div className="flex gap-3">
                    {hasActiveFilters && (
                      <button
                        onClick={() => {
                          onClearFilters()
                          setIsMobileFiltersOpen(false)
                        }}
                        className="flex-1 px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                      >
                        {locale === 'fr' ? 'Effacer tout' : 'Clear All'}
                      </button>
                    )}
                    <button
                      onClick={() => setIsMobileFiltersOpen(false)}
                      className="flex-1 px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200"
                    >
                      {locale === 'fr' ? 'Appliquer' : 'Apply'}
                    </button>
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>

      {/* Enhanced Loading Overlay */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="absolute inset-0 bg-white/90 backdrop-blur-sm flex items-center justify-center z-50 rounded-xl"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="flex flex-col items-center gap-4 p-6 bg-white rounded-lg shadow-lg border border-gray-100"
            >
              <div className="relative">
                <div className="w-8 h-8 border-3 border-red-200 rounded-full"></div>
                <div className="absolute inset-0 w-8 h-8 border-3 border-red-500 border-t-transparent rounded-full animate-spin"></div>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-gray-900 mb-1">
                  {locale === 'fr' ? 'Application des filtres...' : 'Applying filters...'}
                </p>
                <p className="text-xs text-gray-500">
                  {locale === 'fr' ? 'Veuillez patienter' : 'Please wait'}
                </p>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
