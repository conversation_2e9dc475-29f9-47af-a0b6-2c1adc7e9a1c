'use client'
import { cn } from '@/utilities/ui'
import React, { useState, useEffect, useMemo } from 'react'
import { motion, Variants, AnimatePresence } from 'framer-motion'

import { Card, CardPostData, CardProductData } from '@/components/Card'

export type Props = {
  posts: CardPostData[] | CardProductData[]
  relationTo?: 'posts' | 'products'
  locale?: string
  // Filter props
  selectedCategory?: string | null
  selectedFormat?: string | null
  onFilteredCountChange?: (count: number) => void
  showFilters?: boolean
}

export const CollectionArchive: React.FC<Props> = (props) => {
  const {
    posts,
    relationTo = 'posts',
    locale = 'en',
    selectedCategory,
    selectedFormat,
    onFilteredCountChange,
    showFilters = false
  } = props

  const [isFiltering, setIsFiltering] = useState(false)

  // Filter posts based on selected filters
  const filteredPosts = useMemo(() => {
    if (!showFilters || relationTo !== 'products') {
      return posts
    }

    let filtered = [...posts] as (CardPostData | CardProductData)[]

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter((post) => {
        if (typeof post === 'object' && post !== null && 'categories' in post) {
          const categories = (post as any).categories
          if (Array.isArray(categories)) {
            return categories.some((cat: any) => {
              if (typeof cat === 'object' && cat !== null && 'slug' in cat) {
                return cat.slug === selectedCategory
              }
              return false
            })
          }
        }
        return false
      })
    }

    // Filter by format (only for products)
    if (selectedFormat && relationTo === 'products') {
      filtered = filtered.filter((post) => {
        if (typeof post === 'object' && post !== null && 'format' in post) {
          return (post as any).format === selectedFormat
        }
        return false
      })
    }

    return filtered as CardPostData[] | CardProductData[]
  }, [posts, selectedCategory, selectedFormat, showFilters, relationTo])

  // Update filtered count when filters change
  useEffect(() => {
    if (onFilteredCountChange) {
      onFilteredCountChange(filteredPosts.length)
    }
  }, [filteredPosts.length, onFilteredCountChange])

  // Show filtering animation when filters are applied
  useEffect(() => {
    if (showFilters && (selectedCategory || selectedFormat)) {
      setIsFiltering(true)
      const timer = setTimeout(() => setIsFiltering(false), 500)
      return () => clearTimeout(timer)
    }
  }, [selectedCategory, selectedFormat, showFilters])

  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants: Variants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
    },
  }

  return (
    <div className={cn('container mx-auto px-4 sm:px-6 lg:px-8')}>
      {/* Decorative Elements */}
      <div className="relative">
        <div className="absolute -top-20 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-transparent via-red-500 to-transparent opacity-60"></div>
        <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-red-500 rounded-full"></div>
      </div>

      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="relative"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50/30 via-transparent to-red-50/20 rounded-3xl -m-8 -z-10"></div>

        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 lg:gap-10 xl:gap-12 py-16">
          {/* Loading State */}
          {isFiltering && (
            <div className="col-span-full flex items-center justify-center py-12">
              <div className="flex items-center gap-3 text-gray-600">
                <div className="w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-lg font-medium">
                  {locale === 'fr' ? 'Filtrage en cours...' : 'Filtering...'}
                </span>
              </div>
            </div>
          )}

          {/* No Results State */}
          {!isFiltering && filteredPosts.length === 0 && (selectedCategory || selectedFormat) && (
            <div className="col-span-full text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {locale === 'fr' ? 'Aucun produit trouvé' : 'No products found'}
                </h3>
                <p className="text-gray-600">
                  {locale === 'fr'
                    ? 'Essayez de modifier vos filtres pour voir plus de résultats.'
                    : 'Try adjusting your filters to see more results.'
                  }
                </p>
              </div>
            </div>
          )}

          {/* Products Grid */}
          <AnimatePresence mode="wait">
            {!isFiltering && filteredPosts?.map((result, index) => {
              if (typeof result === 'object' && result !== null) {
                return (
                  <motion.div
                    className="flex"
                    key={index}
                    variants={itemVariants}
                    transition={{ duration: 0.6, ease: 'easeOut' }}
                    whileHover={{
                      y: -12,
                      transition: { duration: 0.4, ease: 'easeOut' },
                    }}
                  >
                    <Card
                      className="w-full transform transition-all duration-500 hover:shadow-2xl hover:shadow-red-500/20"
                      doc={result}
                      relationTo={relationTo}
                      showCategories
                      locale={locale}
                    />
                  </motion.div>
                )
              }

              return null
            })}
          </AnimatePresence>
        </div>
      </motion.div>
    </div>
  )
}
