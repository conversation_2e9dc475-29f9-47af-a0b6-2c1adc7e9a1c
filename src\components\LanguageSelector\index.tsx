'use client'

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from '@/components/ui/select'
import React from 'react'
import { Globe } from 'lucide-react'
import { usePathname, useRouter } from 'next/navigation'

import type { Locale } from '../../../middleware'
import {
  getCurrentLocale,
  getAvailableLocales,
  switchLocaleInPathname
} from '@/utilities/locale'

interface LanguageSelectorProps {
  locale: Locale
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({ locale }) => {
  const pathname = usePathname()
  const router = useRouter()
  const availableLocales = getAvailableLocales()

  // Get current locale from pathname or use provided locale
  const currentLocale = getCurrentLocale(pathname) || locale
  const currentLocaleInfo = availableLocales.find(l => l.code === currentLocale)

  // Handle locale change
  const onLocaleChange = (newLocale: Locale) => {
    try {
      // Generate new pathname with the selected locale
      const newPathname = switchLocaleInPathname(pathname, newLocale)

      // Navigate to the new localized URL
      router.push(newPathname)

      // Set cookie for future requests
      document.cookie = `locale-preference=${newLocale}; path=/; max-age=31536000; SameSite=Strict`
    } catch (error) {
      console.error('Error changing locale:', error)
    }
  }

  return (
    <Select onValueChange={onLocaleChange} value={currentLocale}>
      <SelectTrigger
        aria-label="Select a language"
        className="w-auto bg-transparent gap-2 pl-0 md:pl-3 border-none hover:bg-transparent focus:ring-0 focus:ring-offset-0"
      >
        <Globe className="w-4 h-4" />
        <span className="text-sm font-medium">
          {currentLocaleInfo?.label || 'Français'}
        </span>
      </SelectTrigger>
      <SelectContent>
        {availableLocales.map(({ code, label, flag }) => (
          <SelectItem key={code} value={code}>
            <div className="flex items-center gap-2">
              <span className="text-base">{flag}</span>
              <span className="text-sm font-medium">{label}</span>
              <span className="text-xs text-muted-foreground uppercase">{code}</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}
