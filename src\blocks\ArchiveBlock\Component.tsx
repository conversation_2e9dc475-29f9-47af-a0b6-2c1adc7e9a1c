import type { Post, ArchiveBlock as ArchiveBlockProps } from '@/payload-types'

// Temporary Product type until payload types are regenerated
type Product = {
  id: string
  title: string
  slug: string
  heroImage?: any
  categories?: any[]
  populatedAuthors?: any[]
  publishedAt?: string
  price?: number
  currency?: string
  meta?: {
    title?: string | null
    description?: string | null
    image?: any
  } | null
}

import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import RichText from '@/components/RichText'

import { CollectionArchive } from '@/components/CollectionArchive'

export const ArchiveBlock: React.FC<
  ArchiveBlockProps & {
    id?: string
    locale?: string
  }
> = async (props) => {
  const { id, categories, introContent, limit: limitFromProps, populateBy, selectedDocs, relationTo, locale = 'en' } = props

  const limit = limitFromProps || 3

  let posts: (Post | Product)[] = []

  if (populateBy === 'collection') {
    const payload = await getPayload({ config: configPromise })

    const flattenedCategories = categories?.map((category) => {
      if (typeof category === 'object') return category.id
      else return category
    })

    const collection = relationTo || 'posts'
    const fetchedPosts = await payload.find({
      collection: collection as any,
      depth: 2,
      limit,
      ...(flattenedCategories && flattenedCategories.length > 0
        ? {
          where: {
            categories: {
              in: flattenedCategories,
            },
          },
        }
        : {}),
    })

    posts = fetchedPosts.docs
  } else {
    if (selectedDocs?.length) {
      const filteredSelectedPosts = selectedDocs.map((doc) => {
        if (typeof doc.value === 'object') return doc.value
      }).filter(Boolean) as (Post | Product)[]

      posts = filteredSelectedPosts
    }
  }

  return (
    <section className="py-20 lg:py-32 relative overflow-hidden" id={`block-${id}`}>
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50/50 via-white to-red-50/30"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-red-500/5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-red-500/3 rounded-full blur-3xl"></div>

      <div className="relative">
        {introContent && (
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 mb-20">
            <div className="max-w-4xl mx-auto text-center">
              {/* Decorative Top Element */}
              {/* <div className="flex justify-center mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <div className="w-16 h-0.5 bg-gradient-to-r from-red-500 to-red-300"></div>
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div className="w-16 h-0.5 bg-gradient-to-l from-red-500 to-red-300"></div>
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                </div>
              </div> */}

              <div className="relative">
                <RichText
                  className="prose prose-lg lg:prose-xl mx-auto text-gray-700 leading-relaxed"
                  data={introContent}
                  enableGutter={false}
                />

                {/* Decorative Quote Marks */}
                <div className="absolute -top-4 -left-4 text-6xl text-destructive font-serif leading-none opacity-50">
                  &ldquo;
                </div>
                <div className="absolute -bottom-8 -right-4 text-6xl text-destructive font-serif leading-none opacity-50 rotate-180">
                  &rdquo;
                </div>
              </div>
            </div>
          </div>
        )}

        <CollectionArchive posts={posts} relationTo={relationTo as 'posts' | 'products' || 'posts'} locale={locale} />
      </div>
    </section>
  )
}
