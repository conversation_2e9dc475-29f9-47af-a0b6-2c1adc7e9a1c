import type { <PERSON><PERSON><PERSON> } from 'next'
import type { Config } from '@/payload-types'
import { cn } from '@/utilities/ui'
import { DM_Sans as Sans } from 'next/font/google'
import { Playfair_Display } from 'next/font/google'
import { Martel_Sans } from 'next/font/google'
import React from 'react'

import { AdminBar } from '@/components/AdminBar'
import { Footer } from '@/Footer/Component'
import { Header } from '@/Header/Component'
import { Providers } from '@/providers'
import { InitTheme } from '@/providers/Theme/InitTheme'
import { mergeOpenGraph } from '@/utilities/mergeOpenGraph'
import { draftMode } from 'next/headers'

import './globals.css'
import { getServerSideURL } from '@/utilities/getURL'

// Initialize the fonts
const sans = Sans({
  subsets: ['latin'],
  variable: '--font-sans',
})

const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-playfair',
})

// Initialize Martel Sans font with weight 800
const martelSans = Martel_Sans({
  weight: '800',
  subsets: ['latin'],
  variable: '--font-martel-sans',
})

export default async function RootLayout({ children, params }: { children: React.ReactNode, params: Promise<{ lang: Config['locale'] }> }) {
  const { isEnabled } = await draftMode()
  const { lang } = await params
  return (
    <html
      className={cn(sans.variable, playfair.variable, martelSans.variable)}
      lang={lang}
      suppressHydrationWarning
    >
      <head>
        <InitTheme />
        <link href="/favicon.ico" rel="icon" sizes="32x32" />
        <link href="/favicon.svg" rel="icon" type="image/svg+xml" />
      </head>
      <body>
        <Providers>
          <AdminBar
            adminBarProps={{
              preview: isEnabled,
            }}
          />

          <Header locale={lang} />
          <main>{children}</main>
          <Footer />
        </Providers>
      </body>
    </html>
  )
}

export const metadata: Metadata = {
  metadataBase: new URL(getServerSideURL()),
  openGraph: mergeOpenGraph(),
  twitter: {
    card: 'summary_large_image',
    creator: '@payloadcms',
  },
}
