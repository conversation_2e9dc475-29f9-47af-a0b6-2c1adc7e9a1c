'use client'

import { useState, useEffect } from 'react'

/**
 * Hook to detect if code is running on client side
 * Useful for preventing hydration mismatches when using browser-only APIs
 * @returns boolean indicating if code is running on client
 */
export const useIsClient = (): boolean => {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  return isClient
}

export default useIsClient
