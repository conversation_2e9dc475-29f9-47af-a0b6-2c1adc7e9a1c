import type { Block } from 'payload'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import {
  FixedToolbarFeature,
  HeadingFeature,
  InlineToolbarFeature,
  BoldFeature,
  ItalicFeature,
  UnderlineFeature,
} from '@payloadcms/richtext-lexical'

export const StatsCounter: Block = {
  slug: 'statsCounter',
  interfaceName: 'StatsCounterBlock',
  labels: {
    singular: 'Stats Counter',
    plural: 'Stats Counter Blocks',
  },
  fields: [
    {
      name: 'heading',
      type: 'richText',
      label: 'Heading',
      localized: true,
      defaultValue: {
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Our Impact By The Numbers',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            HeadingFeature({ enabledHeadingSizes: ['h2', 'h3', 'h4'] }),
            FixedToolbarFeature(),
            InlineToolbarFeature(),
            BoldFeature(),
            ItalicFeature(),
            UnderlineFeature(),
          ]
        },
      }),
    },
    {
      name: 'description',
      type: 'richText',
      label: 'Description',
      localized: true,
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            FixedToolbarFeature(),
            InlineToolbarFeature(),
            BoldFeature(),
            ItalicFeature(),
            UnderlineFeature(),
          ]
        },
      }),
    },
    {
      name: 'stats',
      type: 'array',
      label: 'Statistics',
      localized: true,
      minRows: 1,
      maxRows: 8,
      labels: {
        singular: 'Statistic',
        plural: 'Statistics',
      },
      fields: [
        {
          name: 'value',
          type: 'number',
          label: 'Value',
          localized: true,
          required: true,
          admin: {
            description: 'The number to count up to',
          },
        },
        {
          name: 'startValue',
          type: 'number',
          label: 'Start Value',
          localized: true,
          defaultValue: 0,
          admin: {
            description: 'Value to start counting from (default: 0)',
          },
        },
        {
          name: 'prefix',
          type: 'text',
          label: 'Prefix',
          localized: true,
          admin: {
            description: 'Text to display before the number (e.g., "$" or "+")',
          },
        },
        {
          name: 'suffix',
          type: 'text',
          label: 'Suffix',
          localized: true,
          admin: {
            description: 'Text to display after the number (e.g., "%" or "k")',
          },
        },
        {
          name: 'decimals',
          type: 'number',
          label: 'Decimal Places',
          localized: true,
          defaultValue: 0,
          min: 0,
          max: 2,
          admin: {
            description: 'Number of decimal places to show (0-2)',
          },
        },
        {
          name: 'title',
          type: 'text',
          label: 'Title',
          localized: true,
          required: true,
          admin: {
            description: 'Title or label for this statistic',
          },
        },
        {
          name: 'description',
          type: 'textarea',
          label: 'Description',
          localized: true,
          admin: {
            description: 'Short description or context for this statistic',
          },
        },
        {
          name: 'icon',
          type: 'select',
          label: 'Icon',
          localized: true,
          options: [
            { label: 'None', value: 'none' },
            { label: 'Growth', value: 'growth' },
            { label: 'User', value: 'user' },
            { label: 'Earth', value: 'earth' },
            { label: 'Clock', value: 'clock' },
            { label: 'Star', value: 'star' },
            { label: 'Award', value: 'award' },
            { label: 'Heart', value: 'heart' },
            { label: 'Recycle', value: 'recycle' },
            { label: 'Chart', value: 'chart' },
          ],
          defaultValue: 'none',
        },
        {
          name: 'visualType',
          type: 'select',
          label: 'Visual Indicator',
          localized: true,
          options: [
            { label: 'None', value: 'none' },
            { label: 'Progress Bar', value: 'progressBar' },
            { label: 'Circle Progress', value: 'circleProgress' },
            { label: 'Bar Chart', value: 'barChart' },
          ],
          defaultValue: 'none',
        },
        {
          name: 'targetValue',
          type: 'number',
          label: 'Target Value',
          localized: true,
          admin: {
            description: 'For progress bars/charts, the maximum value (100%)',
            condition: (data) => data?.visualType !== 'none',
          },
        },
        {
          name: 'color',
          type: 'select',
          label: 'Color',
          localized: true,
          options: [
            { label: 'Primary', value: 'primary' },
            { label: 'Secondary', value: 'secondary' },
            { label: 'Blue', value: 'blue' },
            { label: 'Green', value: 'green' },
            { label: 'Orange', value: 'orange' },
            { label: 'Purple', value: 'purple' },
            { label: 'Red', value: 'red' },
          ],
          defaultValue: 'primary',
        },
        {
          name: 'highlighted',
          type: 'checkbox',
          label: 'Highlight',
          localized: true,
          defaultValue: false,
        },
      ],
      defaultValue: [
        {
          value: 120000,
          prefix: '+',
          title: 'Customers Served',
          icon: 'user',
          color: 'primary',
        },
        {
          value: 97,
          suffix: '%',
          title: 'Customer Satisfaction',
          visualType: 'progressBar',
          targetValue: 100,
          color: 'green',
        },
        {
          value: 50,
          suffix: '%',
          title: 'Carbon Reduction',
          description: 'Since 2020',
          icon: 'recycle',
          color: 'blue',
        },
        {
          value: 24,
          suffix: '/7',
          title: 'Support Available',
          icon: 'clock',
          color: 'secondary',
        },
      ],
    },
    {
      name: 'animationSettings',
      type: 'group',
      label: 'Animation Settings',
      localized: true,
      fields: [
        {
          name: 'duration',
          type: 'number',
          label: 'Animation Duration',
          localized: true,
          defaultValue: 2000,
          min: 500,
          max: 5000,
          admin: {
            description: 'Duration of the counter animation in milliseconds (500-5000)',
          },
        },
        {
          name: 'delay',
          type: 'number',
          label: 'Animation Delay',
          localized: true,
          defaultValue: 200,
          min: 0,
          max: 1000,
          admin: {
            description: 'Delay between starting each counter in milliseconds (0-1000)',
          },
        },
        {
          name: 'easing',
          type: 'select',
          label: 'Easing Function',
          localized: true,
          options: [
            { label: 'Linear', value: 'linear' },
            { label: 'Ease Out', value: 'easeOut' },
            { label: 'Ease In Out', value: 'easeInOut' },
            { label: 'Elastic', value: 'elastic' },
            { label: 'Bounce', value: 'bounce' },
          ],
          defaultValue: 'easeOut',
        },
        {
          name: 'triggerOnce',
          type: 'checkbox',
          label: 'Trigger Animation Once',
          localized: true,
          defaultValue: true,
          admin: {
            description: 'If checked, animation will only play once when scrolled into view',
          },
        },
        {
          name: 'staggered',
          type: 'checkbox',
          label: 'Staggered Animation',
          localized: true,
          defaultValue: true,
          admin: {
            description: 'If checked, counters will animate one after another',
          },
        },
      ],
    },
    {
      name: 'layout',
      type: 'group',
      label: 'Layout Settings',
      localized: true,
      fields: [
        {
          name: 'columns',
          type: 'select',
          label: 'Grid Columns',
          localized: true,
          options: [
            { label: 'Auto (responsive)', value: 'auto' },
            { label: '1 Column', value: '1' },
            { label: '2 Columns', value: '2' },
            { label: '3 Columns', value: '3' },
            { label: '4 Columns', value: '4' },
          ],
          defaultValue: 'auto',
        },
        {
          name: 'alignment',
          type: 'select',
          label: 'Content Alignment',
          localized: true,
          options: [
            { label: 'Center', value: 'center' },
            { label: 'Left', value: 'left' },
            { label: 'Right', value: 'right' },
          ],
          defaultValue: 'center',
        },
        {
          name: 'padding',
          type: 'select',
          label: 'Padding',
          localized: true,
          options: [
            { label: 'Small', value: 'small' },
            { label: 'Medium', value: 'medium' },
            { label: 'Large', value: 'large' },
          ],
          defaultValue: 'medium',
        },
        {
          name: 'backgroundColor',
          type: 'select',
          label: 'Background Color',
          localized: true,
          options: [
            { label: 'None', value: 'none' },
            { label: 'Light', value: 'light' },
            { label: 'Dark', value: 'dark' },
            { label: 'Primary Light', value: 'primaryLight' },
            { label: 'Secondary Light', value: 'secondaryLight' },
          ],
          defaultValue: 'none',
        },
        {
          name: 'textColor',
          type: 'select',
          label: 'Text Color',
          localized: true,
          options: [
            { label: 'Default', value: 'default' },
            { label: 'Light', value: 'light' },
            { label: 'Dark', value: 'dark' },
          ],
          defaultValue: 'default',
        },
        {
          name: 'cardStyle',
          type: 'checkbox',
          label: 'Use Card Style',
          localized: true,
          defaultValue: false,
          admin: {
            description: 'Display each statistic in a card with shadow and border',
          },
        },
        {
          name: 'showDividers',
          type: 'checkbox',
          label: 'Show Dividers',
          localized: true,
          defaultValue: false,
          admin: {
            description: 'Show dividing lines between statistics',
            condition: (data) => data?.cardStyle === false,
          },
        },
      ],
    },
  ],
}

export default StatsCounter
