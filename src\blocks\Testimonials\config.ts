import type { Field, Block } from 'payload'

export const TestimonialsBlock: Block = {
  slug: 'testimonials',
  labels: {
    singular: 'Testimonials Block',
    plural: 'Testimonials Blocks',
  },
  graphQL: {
    singularName: 'TestimonialsBlock',
  },
  fields: [
    {
      type: 'row',
      fields: [
        {
          name: 'heading',
          type: 'text',
          localized: true,
          required: true,
          defaultValue: 'Ils nous font confiance',
          admin: {
            description: 'Main heading displayed at the top of the testimonials block.',
            width: '50%',
          },
        },
        {
          name: 'headingColor',
          type: 'text',
          localized: true,
          defaultValue: '#E01B3F',
          admin: {
            description: 'Heading text color (hex code). Default is Cuisina red.',
            width: '50%',
          },
        },
      ],
    },
    {
      name: 'subheading',
      type: 'text',
      localized: true,
      admin: {
        description: 'Subheading displayed below the main heading.',
      },
    },
    {
      name: 'testimonials',
      type: 'array',
      localized: true,
      minRows: 1,
      maxRows: 6,
      admin: {
        description: 'Add testimonials to display in the slider. Best with 3-6 testimonials.',
      },
      fields: [
        {
          name: 'quote',
          type: 'textarea',
          localized: true,
          required: true,
          admin: {
            description: 'The testimonial quote text.',
          },
        },
        {
          type: 'row',
          fields: [
            {
              name: 'author',
              type: 'text',
              localized: true,
              required: true,
              admin: {
                description: 'Name of the person giving the testimonial.',
                width: '50%',
              },
            },
            {
              name: 'location',
              type: 'text',
              localized: true,
              admin: {
                description: 'Location or company of the testimonial author.',
                width: '50%',
              },
            },
          ],
        },
        {
          name: 'quoteColor',
          type: 'text',
          localized: true,
          defaultValue: '#E01B3F',
          admin: {
            description: 'Color for the quote marks (hex code). Default is Cuisina red.',
          },
        },
      ],
    },
    {
      name: 'backgroundColor',
      type: 'select',
      localized: true,
      defaultValue: 'white',
      options: [
        { label: 'White', value: 'white' },
        { label: 'Light Gray', value: 'light-gray' },
        { label: 'Off White', value: 'off-white' },
      ],
      admin: {
        description: 'Background color for the block',
      },
    },
  ],
}
