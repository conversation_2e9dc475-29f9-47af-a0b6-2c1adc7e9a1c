import type { Metadata } from 'next/types'

import configPromise from '@payload-config'
import { getPayload } from 'payload'
import React from 'react'
import PageClient from './page.client'
import { ProductsPageClient } from './ProductsPageClient'
import { ProductCategory } from '@/components/ProductFilters'

export const dynamic = 'force-static'
export const revalidate = 600

export default async function Page({ params }: { params: Promise<{ lang: string }> }) {
  const { lang } = await params
  const payload = await getPayload({ config: configPromise })

  // Fetch products
  const products = await payload.find({
    collection: 'products' as any,
    depth: 2,
    limit: 50, // Increased limit for better filtering
    overrideAccess: false,
    locale: lang as any,
    select: {
      title: true,
      slug: true,
      categories: true,
      format: true,
      heroImage: true,
      meta: true,
    },
  })

  // Fetch product categories
  const categoriesResult = await payload.find({
    collection: 'product-categories' as any,
    depth: 1,
    limit: 100,
    overrideAccess: false,
    locale: lang as any,
    select: {
      title: true,
      slug: true,
    },
  })

  // Transform categories to match ProductCategory interface
  const categories: ProductCategory[] = categoriesResult.docs.map((cat: any) => ({
    id: cat.id,
    title: cat.title,
    slug: cat.slug,
  }))

  return (
    <>
      <PageClient />
      <ProductsPageClient
        initialProducts={products.docs as any}
        categories={categories}
        locale={lang}
        totalProducts={products.totalDocs}
      />
    </>
  )
}

export async function generateMetadata({ params }: { params: Promise<{ lang: string }> }): Promise<Metadata> {
  const { lang } = await params

  const title = lang === 'fr' ? 'Nos Produits' : 'Our Products'
  const description = lang === 'fr'
    ? 'Découvrez notre collection de meubles et produits pour la maison'
    : 'Browse our collection of furniture and home products'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
    },
  }
}
