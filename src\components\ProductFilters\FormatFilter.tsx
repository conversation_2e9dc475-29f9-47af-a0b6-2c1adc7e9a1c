'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, Check } from 'lucide-react'
import { cn } from '@/utilities/ui'

export interface FormatOption {
  label: string
  value: string
}

interface FormatFilterProps {
  formats: FormatOption[]
  selectedFormat: string | null
  onFormatChange: (formatValue: string | null) => void
  className?: string
  locale?: string
  isVisible?: boolean
}

export const FormatFilter: React.FC<FormatFilterProps> = ({
  formats,
  selectedFormat,
  onFormatChange,
  className,
  locale = 'en',
  isVisible = true
}) => {
  const [isOpen, setIsOpen] = useState(false)

  // Get selected format display name
  const selectedFormatName = selectedFormat 
    ? formats.find(format => format.value === selectedFormat)?.label || 'Unknown Format'
    : locale === 'fr' ? 'Tous les formats' : 'All Formats'

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('[data-format-filter]')) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  // Close dropdown when visibility changes
  useEffect(() => {
    if (!isVisible) {
      setIsOpen(false)
    }
  }, [isVisible])

  if (!isVisible) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.3 }}
      className={cn('relative', className)}
      data-format-filter
    >
      {/* Filter Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'flex items-center justify-between w-full px-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm hover:border-red-300 hover:shadow-md transition-all duration-200',
          isOpen && 'border-red-500 shadow-md ring-2 ring-red-500/20'
        )}
      >
        <div className="flex items-center gap-3">
          <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
          <span className="font-medium text-gray-900">
            {locale === 'fr' ? 'Format:' : 'Format:'} {selectedFormatName}
          </span>
        </div>
        <motion.div
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronDown className="w-5 h-5 text-gray-500" />
        </motion.div>
      </button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-40 overflow-hidden"
          >
            {/* Options List */}
            <div className="max-h-64 overflow-y-auto">
              {/* All Formats Option */}
              <button
                onClick={() => {
                  onFormatChange(null)
                  setIsOpen(false)
                }}
                className={cn(
                  'flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150',
                  !selectedFormat && 'bg-orange-50 text-orange-700'
                )}
              >
                <span className="font-medium">
                  {locale === 'fr' ? 'Tous les formats' : 'All Formats'}
                </span>
                {!selectedFormat && (
                  <Check className="w-4 h-4 text-orange-600" />
                )}
              </button>

              {/* Format Options */}
              {formats.map((format) => (
                <button
                  key={format.value}
                  onClick={() => {
                    onFormatChange(format.value)
                    setIsOpen(false)
                  }}
                  className={cn(
                    'flex items-center justify-between w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-150',
                    selectedFormat === format.value && 'bg-orange-50 text-orange-700'
                  )}
                >
                  <div className="flex flex-col items-start">
                    <span className="font-medium">{format.label}</span>
                  </div>
                  {selectedFormat === format.value && (
                    <Check className="w-4 h-4 text-orange-600" />
                  )}
                </button>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
