import type { Block } from 'payload'

export const PartnersLogos: Block = {
    slug: 'partnersLogos',
    interfaceName: 'PartnersLogosBlock',
    labels: {
        singular: 'Partners Logos',
        plural: 'Partners Logos Blocks',
    },
    fields: [
        {
            name: 'title',
            type: 'text',
            label: 'Title',
            localized: true,
            admin: {
                description: 'Optional title for the partners section (e.g., "Our Partners", "Trusted By")',
            },
        },
        {
            name: 'subtitle',
            type: 'text',
            label: 'Subtitle',
            localized: true,
            admin: {
                description: 'Optional subtitle or description',
            },
        },
        {
            name: 'partners',
            type: 'array',
            label: 'Partners',
            localized: true,
            labels: {
                singular: 'Partner',
                plural: 'Partners',
            },
            fields: [
                {
                    name: 'logo',
                    type: 'upload',
                    label: 'Partner Logo',
                    relationTo: 'media',
                    localized: true,
                    required: true,
                    admin: {
                        description: 'Upload the partner logo image (PNG or SVG recommended for best quality)',
                    },
                },
                {
                    name: 'name',
                    type: 'text',
                    label: 'Partner Name',
                    localized: true,
                    required: true,
                    admin: {
                        description: 'Name of the partner company (used for alt text and accessibility)',
                    },
                },
                {
                    name: 'website',
                    type: 'text',
                    label: 'Website URL',
                    localized: true,
                    admin: {
                        description: 'Optional website URL - logos will be clickable if provided (include https://)',
                    },
                },
            ],
            admin: {
                initCollapsed: false,
                description: 'Add partner logos that will scroll horizontally from right to left',
            },
            minRows: 1,
            defaultValue: [
                {
                    name: 'Partner 1',
                },
                {
                    name: 'Partner 2',
                },
                {
                    name: 'Partner 3',
                },
                {
                    name: 'Partner 4',
                },
                {
                    name: 'Partner 5',
                },
            ],
        },
        {
            name: 'design',
            type: 'group',
            label: 'Design Settings',
            localized: true,
            fields: [
                {
                    name: 'backgroundColor',
                    type: 'select',
                    label: 'Background Color',
                    localized: true,
                    defaultValue: 'white',
                    options: [
                        { label: 'White', value: 'white' },
                        { label: 'Light Gray', value: 'light-gray' },
                        { label: 'Primary Light', value: 'primary-light' },
                        { label: 'Secondary Light', value: 'secondary-light' },
                        { label: 'Transparent', value: 'transparent' },
                    ],
                    admin: {
                        description: 'Background color for the partners section',
                    },
                },
                {
                    name: 'logoSize',
                    type: 'select',
                    label: 'Logo Size',
                    localized: true,
                    defaultValue: 'medium',
                    options: [
                        { label: 'Small', value: 'small' },
                        { label: 'Medium', value: 'medium' },
                        { label: 'Large', value: 'large' },
                    ],
                    admin: {
                        description: 'Size of the partner logos',
                    },
                },
                {
                    name: 'animationSpeed',
                    type: 'select',
                    label: 'Animation Speed',
                    localized: true,
                    defaultValue: 'medium',
                    options: [
                        { label: 'Slow', value: 'slow' },
                        { label: 'Medium', value: 'medium' },
                        { label: 'Fast', value: 'fast' },
                    ],
                    admin: {
                        description: 'Speed of the scrolling animation',
                    },
                },
                {
                    name: 'padding',
                    type: 'select',
                    label: 'Section Padding',
                    localized: true,
                    defaultValue: 'medium',
                    options: [
                        { label: 'Small', value: 'small' },
                        { label: 'Medium', value: 'medium' },
                        { label: 'Large', value: 'large' },
                    ],
                    admin: {
                        description: 'Vertical padding around the section',
                    },
                },
                {
                    name: 'showBorder',
                    type: 'checkbox',
                    label: 'Show Top/Bottom Borders',
                    localized: true,
                    defaultValue: false,
                    admin: {
                        description: 'Add subtle borders above and below the section',
                    },
                },
                {
                    name: 'grayscale',
                    type: 'checkbox',
                    label: 'Grayscale Logos',
                    localized: true,
                    defaultValue: false,
                    admin: {
                        description: 'Display logos in grayscale by default',
                    },
                },
                {
                    name: 'hoverEffect',
                    type: 'checkbox',
                    label: 'Hover Effects',
                    localized: true,
                    defaultValue: true,
                    admin: {
                        description: 'Enable hover effects (scale and color restoration)',
                    },
                },
            ],
            admin: {
                description: 'Customize the appearance and behavior of the partners section',
            },
        },
    ],
}

export default PartnersLogos
