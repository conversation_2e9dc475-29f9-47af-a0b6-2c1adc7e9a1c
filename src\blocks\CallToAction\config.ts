import { Block } from 'payload'
import { link } from '@/fields/link'
import { lexicalEditor } from '@payloadcms/richtext-lexical'

export const CallToAction: Block = {
  slug: 'CallToAction',
  labels: {
    singular: 'Call to Action',
    plural: 'Call to Action Blocks',
  },
  fields: [
    {
      name: 'preset',
      type: 'select',
      label: 'Style Preset',
      localized: true,
      options: [
        { label: 'Custom', value: 'custom' },
        { label: "CUISINA Let's Build", value: 'kwmBuild' },
      ],
      defaultValue: 'custom',
      admin: {
        description: 'Choose a pre-configured style or customize settings below',
      },
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Content',
          fields: [
            {
              name: 'heading',
              label: 'Heading',
              type: 'text',
              localized: true,
              required: true,
              admin: {
                description: 'The main heading for the call to action',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
            {
              name: 'richText',
              type: 'richText',
              label: 'Description',
              localized: true,
              editor: lexicalEditor({
                features: ({ rootFeatures }) => rootFeatures,
              }),
              admin: {
                description: (data: any) => {
                  if (data?.preset === 'kwmBuild') {
                    return 'The descriptive text about working with CUISINA as a trusted manufacturing partner'
                  }
                  return 'Optional description text'
                },
                condition: (data: any) => data?.preset !== 'kwmBuild' || !data?.preset,
              },
            },
            {
              name: 'textAlignment',
              type: 'select',
              localized: true,
              defaultValue: 'center',
              options: [
                { label: 'Left', value: 'left' },
                { label: 'Center', value: 'center' },
                { label: 'Right', value: 'right' },
              ],
              label: 'Text Alignment',
              admin: {
                description: 'Horizontal alignment of the content',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
            {
              name: 'textSize',
              type: 'select',
              localized: true,
              defaultValue: 'medium',
              options: [
                { label: 'Small', value: 'small' },
                { label: 'Medium', value: 'medium' },
                { label: 'Large', value: 'large' },
              ],
              label: 'Text Size',
              admin: {
                description: 'Size of the body text',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
            {
              name: 'maxWidth',
              type: 'select',
              localized: true,
              defaultValue: 'medium',
              options: [
                { label: 'Narrow (640px)', value: 'narrow' },
                { label: 'Medium (768px)', value: 'medium' },
                { label: 'Wide (896px)', value: 'wide' },
                { label: 'Full Width', value: 'full' },
              ],
              label: 'Maximum Width',
              admin: {
                description: 'Maximum width of the content container',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
            {
              name: 'containerWidth',
              type: 'select',
              localized: true,
              defaultValue: 'default',
              options: [
                { label: 'Default (1024px)', value: 'default' },
                { label: 'Narrow (768px)', value: 'narrow' },
                { label: 'Wide (1280px)', value: 'wide' },
                { label: 'Full Width', value: 'full' },
              ],
              label: 'Container Width',
              admin: {
                description: 'Width of the outer container',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
          ],
        },
        {
          label: 'Buttons',
          fields: [
            {
              type: 'row',
              fields: [
                {
                  name: 'enablePrimaryLink',
                  type: 'checkbox',
                  label: 'Primary Button',
                  localized: true,
                  defaultValue: true,
                  admin: {
                    description: 'Enable primary call-to-action button',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
                {
                  name: 'enableSecondaryLink',
                  type: 'checkbox',
                  label: 'Secondary Button',
                  localized: true,
                  defaultValue: true,
                  admin: {
                    description: 'Enable secondary call-to-action button',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
              ],
            },
            link({
              appearances: ['default', 'accent'],
              overrides: {
                name: 'primaryLink',
                label: 'Primary Button',
                admin: {
                  description: (data: any) => {
                    if (data?.preset === 'kwmBuild') {
                      return 'Configure the "Contact Us" button'
                    }
                    return 'Configure the primary call-to-action button'
                  },
                  condition: (data: any, siblingData: any) =>
                    data?.preset === 'kwmBuild' || Boolean(siblingData?.enablePrimaryLink),
                },
              },
            }),
            link({
              appearances: ['outline', 'accent-outline'],
              overrides: {
                name: 'secondaryLink',
                label: 'Secondary Button',
                admin: {
                  description: 'Configure the secondary call-to-action button',
                  condition: (data: any, siblingData: any) =>
                    data?.preset !== 'kwmBuild' && Boolean(siblingData?.enableSecondaryLink),
                },
              },
            }),
            {
              type: 'row',
              fields: [
                {
                  name: 'buttonSize',
                  type: 'select',
                  localized: true,
                  defaultValue: 'lg',
                  options: [
                    { label: 'Small', value: 'sm' },
                    { label: 'Default', value: 'default' },
                    { label: 'Large', value: 'lg' },
                  ],
                  label: 'Button Size',
                  admin: {
                    description: 'Size of the CTA buttons',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
                {
                  name: 'buttonLayout',
                  type: 'select',
                  localized: true,
                  defaultValue: 'inline',
                  options: [
                    { label: 'Inline (Side by Side)', value: 'inline' },
                    { label: 'Stacked (Vertical)', value: 'stacked' },
                  ],
                  label: 'Button Layout',
                  admin: {
                    description: 'How to arrange multiple buttons',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
              ],
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'buttonAlignment',
                  type: 'select',
                  localized: true,
                  defaultValue: 'center',
                  options: [
                    { label: 'Left', value: 'left' },
                    { label: 'Center', value: 'center' },
                    { label: 'Right', value: 'right' },
                  ],
                  label: 'Button Alignment',
                  admin: {
                    description: 'Horizontal alignment of the buttons',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
                {
                  name: 'buttonGap',
                  type: 'select',
                  localized: true,
                  defaultValue: 'medium',
                  options: [
                    { label: 'Small', value: 'small' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Large', value: 'large' },
                  ],
                  label: 'Button Spacing',
                  admin: {
                    description: 'Amount of space between buttons',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
              ],
            },
          ],
        },
        {
          label: 'Layout',
          fields: [
            {
              name: 'variant',
              type: 'select',
              localized: true,
              defaultValue: 'standard',
              options: [
                { label: 'Standard (Boxed)', value: 'standard' },
                { label: 'Full Width', value: 'full' },
                { label: 'Compact', value: 'compact' },
                { label: 'Banner', value: 'banner' },
                { label: 'Card', value: 'card' },
              ],
              admin: {
                description: 'Overall layout style of the CTA block',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
            {
              name: 'spacing',
              type: 'select',
              localized: true,
              defaultValue: 'normal',
              options: [
                { label: 'Tight', value: 'tight' },
                { label: 'Normal', value: 'normal' },
                { label: 'Loose', value: 'loose' },
              ],
              label: 'Vertical Spacing',
              admin: {
                description: 'Amount of vertical padding around the content',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'padding',
                  type: 'select',
                  localized: true,
                  options: [
                    { label: 'None', value: 'py-0' },
                    { label: 'Small (py-8)', value: 'py-8' },
                    { label: 'Medium (py-12)', value: 'py-12' },
                    { label: 'Large (py-16)', value: 'py-16' },
                    { label: 'Extra Large (py-24)', value: 'py-24' },
                  ],
                  defaultValue: 'py-16',
                  admin: {
                    description: 'Inner vertical padding of the section',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
                {
                  name: 'borderRadius',
                  type: 'select',
                  localized: true,
                  options: [
                    { label: 'None', value: 'none' },
                    { label: 'Small', value: 'small' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Large', value: 'large' },
                  ],
                  defaultValue: 'medium',
                  admin: {
                    description: 'Roundness of the corners (when applicable)',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
              ],
            },
          ],
        },
        {
          label: 'Styling',
          fields: [
            {
              type: 'row',
              fields: [
                {
                  name: 'backgroundColor',
                  type: 'select',
                  localized: true,
                  options: [
                    { label: 'Background', value: 'bg-background' },
                    { label: 'Card', value: 'bg-card' },
                    { label: 'Primary', value: 'bg-primary' },
                    { label: 'Secondary', value: 'bg-secondary' },
                    { label: 'Accent', value: 'bg-accent' },
                    { label: 'Muted', value: 'bg-muted' },
                    { label: 'Primary Subtle', value: 'bg-primary-subtle' },
                    { label: 'Secondary Subtle', value: 'bg-secondary-subtle' },
                    { label: 'White', value: 'bg-white' },
                  ],
                  defaultValue: 'bg-background',
                  admin: {
                    description: 'Background color of the section (theme-aware)',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
                {
                  name: 'textColor',
                  type: 'select',
                  localized: true,
                  options: [
                    { label: 'Default', value: 'text-foreground' },
                    { label: 'White', value: 'text-white' },
                    { label: 'Primary', value: 'text-primary' },
                    { label: 'Secondary', value: 'text-secondary' },
                    { label: 'Accent', value: 'text-accent' },
                    { label: 'Muted', value: 'text-muted-foreground' },
                  ],
                  defaultValue: 'text-foreground',
                  admin: {
                    description: 'Text color for the content (theme-aware)',
                    width: '50%',
                    condition: (data: any) => data?.preset !== 'kwmBuild',
                  },
                },
              ],
            },
            {
              name: 'backgroundGradient',
              type: 'checkbox',
              label: 'Enable Background Gradient',
              localized: true,
              defaultValue: false,
              admin: {
                description: 'Add a subtle gradient effect to the background',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
            {
              name: 'containerColor',
              type: 'select',
              localized: true,
              defaultValue: 'border',
              options: [
                { label: 'Default (Border Only)', value: 'border' },
                { label: 'Primary (10% Opacity)', value: 'primary-10' },
                { label: 'Secondary (10% Opacity)', value: 'secondary-10' },
                { label: 'Muted (10% Opacity)', value: 'muted-10' },
                { label: 'Accent (10% Opacity)', value: 'accent-10' },
                { label: 'Card', value: 'card' },
              ],
              admin: {
                description: 'Background color of the inner container with theme-aware colors',
                condition: (data: any, siblingData: any) =>
                  data?.preset !== 'kwmBuild' && siblingData?.variant !== 'full',
              },
            },
            {
              name: 'backgroundTexture',
              type: 'select',
              localized: true,
              defaultValue: 'none',
              options: [
                { label: 'None', value: 'none' },
                { label: 'Dots', value: 'dots' },
                { label: 'Grid', value: 'grid' },
                { label: 'Lines', value: 'lines' },
                { label: 'Waves', value: 'waves' },
                { label: 'Noise', value: 'noise' },
              ],
              admin: {
                description: 'Apply a texture pattern to the container background',
                condition: (data: any, siblingData: any) =>
                  data?.preset !== 'kwmBuild' && siblingData?.variant !== 'full',
              },
            },
            {
              name: 'textureTarget',
              label: 'Apply Texture To',
              type: 'select',
              localized: true,
              defaultValue: 'container',
              options: [
                { label: 'Container Only', value: 'container' },
                { label: 'Entire Block', value: 'block' },
              ],
              admin: {
                description:
                  'Choose whether the texture applies to just the container or the entire block background',
                condition: (data: any, siblingData: any) =>
                  data?.preset !== 'kwmBuild' &&
                  siblingData?.backgroundTexture &&
                  siblingData?.backgroundTexture !== 'none',
              },
            },
            {
              name: 'textureOpacity',
              label: 'Texture Opacity',
              type: 'select',
              localized: true,
              defaultValue: 'medium',
              options: [
                { label: 'Very Light', value: 'very-light' },
                { label: 'Light', value: 'light' },
                { label: 'Medium', value: 'medium' },
                { label: 'Strong', value: 'strong' },
                { label: 'Very Strong', value: 'very-strong' },
              ],
              admin: {
                description: 'Opacity level of the texture pattern',
                condition: (data: any, siblingData: any) =>
                  data?.preset !== 'kwmBuild' &&
                  siblingData?.backgroundTexture &&
                  siblingData?.backgroundTexture !== 'none',
              },
            },
            {
              name: 'enableBorder',
              type: 'checkbox',
              label: 'Enable Border',
              localized: true,
              defaultValue: true,
              admin: {
                description: 'Add a border around the container',
                condition: (data: any, siblingData: any) =>
                  data?.preset !== 'kwmBuild' && siblingData?.variant !== 'full',
              },
            },
            {
              type: 'row',
              fields: [
                {
                  name: 'borderWidth',
                  type: 'select',
                  localized: true,
                  options: [
                    { label: 'None', value: 'none' },
                    { label: 'Thin', value: 'thin' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Thick', value: 'thick' },
                  ],
                  defaultValue: 'thin',
                  admin: {
                    description: 'Thickness of the border',
                    condition: (data: any, siblingData: any) =>
                      data?.preset !== 'kwmBuild' &&
                      siblingData?.enableBorder &&
                      siblingData?.variant !== 'full',
                  },
                },
                {
                  name: 'borderColor',
                  type: 'select',
                  localized: true,
                  options: [
                    { label: 'Default', value: 'border-border' },
                    { label: 'Primary', value: 'border-primary' },
                    { label: 'Secondary', value: 'border-secondary' },
                    { label: 'Accent', value: 'border-accent' },
                    { label: 'Muted', value: 'border-muted' },
                  ],
                  defaultValue: 'border-border',
                  admin: {
                    description: 'Color of the border using theme colors',
                    condition: (data: any, siblingData: any) =>
                      data?.preset !== 'kwmBuild' &&
                      siblingData?.enableBorder &&
                      siblingData?.variant !== 'full' &&
                      siblingData?.borderWidth !== 'none',
                  },
                },
              ],
            },
            {
              name: 'shadow',
              type: 'select',
              localized: true,
              options: [
                { label: 'None', value: 'none' },
                { label: 'Small', value: 'small' },
                { label: 'Medium', value: 'medium' },
                { label: 'Large', value: 'large' },
              ],
              defaultValue: 'none',
              admin: {
                description: 'Add a shadow to the container',
                condition: (data: any) => data?.preset !== 'kwmBuild',
              },
            },
          ],
        },
      ],
    },
    // Hidden fields for CUISINA preset
    {
      name: 'kwmHeading',
      type: 'text',
      localized: true,
      defaultValue: "Let's Build Something That Lasts",
      label: 'Heading',
      admin: {
        condition: (data: any) => data?.preset === 'kwmBuild',
      },
    },
    {
      name: 'kwmDescription',
      type: 'text',
      localized: true,
      defaultValue:
        'Ready to work with a trusted manufacturing partner who delivers on quality, ethics, and time?',
      label: 'Description Text',
      admin: {
        description: 'Description text about working with CUISINA',
        condition: (data: any) => data?.preset === 'kwmBuild',
      },
    },
    {
      name: 'kwmButtonLabel',
      type: 'text',
      localized: true,
      defaultValue: 'Contact Us',
      label: 'Button Label',
      admin: {
        description: 'Text for the call-to-action button',
        condition: (data: any) => data?.preset === 'kwmBuild',
      },
    },
    {
      name: 'kwmImage',
      type: 'upload',
      relationTo: 'media',
      localized: true,
      required: true,
      label: 'Right Side Image',
      admin: {
        description: 'Image to display on the right side of the section',
        condition: (data: any) => data?.preset === 'kwmBuild',
      },
    },
  ],
}
