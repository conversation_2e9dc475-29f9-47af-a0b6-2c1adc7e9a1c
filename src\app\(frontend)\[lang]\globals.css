@tailwind base;
@tailwind components;
@tailwind utilities;

/* Theme initialization styles - prevent hydration issues */
html[data-theme=light] { color-scheme: light; }
html[data-theme=dark] { color-scheme: dark; }
html { transition: color-scheme 180ms ease-out; }
html:not([data-theme]) { color-scheme: light; }

:root {
  --admin-bar-height: 0px;

  /* RGB values for background colors to use with opacity */
  --background-rgb: 255, 255, 255;

  /* Font size scaling variable */
  --font-size-scale: 1;
}

/* Admin Bar offset styles */
body {
  padding-top: var(--admin-bar-height);
  transition: padding-top 0.3s ease;
}

/* Animation utilities for components */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.8s ease-in-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out forwards;
}

/* Adjust sticky and fixed headers to respect admin bar height */
.sticky-header,
.fixed-header,
.sticky-top,
.navbar.sticky-top,
.navbar.fixed-top,
header.sticky,
header.fixed,
.sticky,
.fixed-top,
[class*='sticky-'],
[class*='fixed-'] {
  top: var(--admin-bar-height) !important;
  transition: top 0.3s ease;
}

@layer components {
  .header-spacing {
    padding: max(
      6rem,
      calc(6rem + var(--admin-bar-height))
    ); /* Base header height + admin bar height */
  }

  /* Add consistent padding for containers on all screen sizes */
  .container {
    @apply px-5 sm:px-6 md:px-8 lg:px-10 mx-auto max-w-[1440px];
  }
}

@layer base {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-size: unset;
    font-weight: 400;
    font-family: var(--font-playfair);
  }

  body {
    font-family: var(--font-playfair);
  }

  /* :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 240 5% 96%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;

    --border: 240 6% 80%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.2rem;

    --success: 196 52% 74%;
    --warning: 34 89% 85%;
    --error: 10 100% 86%;
  } */

  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 351 99% 39%; /* Exact logo color: #C9011E */
    --accent-foreground: 351 10% 97%; /* Light text on the accent color */
    --accent-green: 142 72% 29%; /* Rich green color: #1e8a3e */
    --accent-green-foreground: 0 0% 98%; /* Light text on the accent green */
    --destructive: 355, 75%, 42%; /* Red color */
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  [data-theme='dark'] {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 346 83.9% 64.7%;
    --accent-foreground: 0 0% 98%;
    --accent-green: 142 76% 45%; /* Brighter green for dark mode: #27c653 */
    --accent-green-foreground: 0 0% 98%; /* Light text on the accent green */
    --destructive: 0 84% 45%; /* Red color for dark theme */
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Dark theme RGB values */
    --background-rgb: 10, 10, 10;
  }
}

/* [data-theme='dark'] {
    --background: 0 0% 0%;
    --foreground: 210 40% 98%;

    --card: 0 0% 4%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84% 45%;
    --destructive-foreground: 210 40% 98%;

    --border: 0, 0%, 15%, 0.8;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --success: 196 100% 14%;
    --warning: 34 51% 25%;
    --error: 10 39% 43%;
  }
} */

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Accent green utility classes */
  .bg-accent-green {
    background-color: hsl(var(--accent-green));
  }
  .text-accent-green {
    color: hsl(var(--accent-green));
  }
  .border-accent-green {
    border-color: hsl(var(--accent-green));
  }
  .bg-accent-green-foreground {
    background-color: hsl(var(--accent-green-foreground));
  }
  .text-accent-green-foreground {
    color: hsl(var(--accent-green-foreground));
  }
  .bg-accent-green-light {
    background-color: hsl(var(--accent-green-light));
  }
  .text-accent-green-light {
    color: hsl(var(--accent-green-light));
  }
  .border-accent-green-light {
    border-color: hsl(var(--accent-green-light));
  }
  .bg-accent-green-light-foreground {
    background-color: hsl(var(--accent-green-light-foreground));
  }
  .text-accent-green-light-foreground {
    color: hsl(var(--accent-green-light-foreground));
  }
  .bg-accent-green-dark {
    background-color: hsl(var(--accent-green-dark));
  }
  .text-accent-green-dark {
    color: hsl(var(--accent-green-dark));
  }
  .border-accent-green-dark {
    border-color: hsl(var(--accent-green-dark));
  }
  .bg-accent-green-dark-foreground {
    background-color: hsl(var(--accent-green-dark-foreground));
  }
  .text-accent-green-dark-foreground {
    color: hsl(var(--accent-green-dark-foreground));
  }
}

@layer base {
  body {
    @apply bg-background text-foreground min-h-[100vh] flex flex-col;
  }
}

/* Global styles outside of Tailwind layers */
html {
  opacity: 0;
  font-size: calc(100% * var(--font-size-scale));
}

html[data-theme='dark'],
html[data-theme='light'] {
  opacity: initial;
}

/* Full-width block styles */
.full-width-block {
  width: 100vw !important;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
  max-width: none !important;
}

/* SlidingText faded edges styles */
.sliding-text-faded-container {
  position: relative;
  mask-image: linear-gradient(to right, transparent, black 10%, black 90%, transparent 100%);
  -webkit-mask-image: linear-gradient(
    to right,
    transparent,
    black 10%,
    black 90%,
    transparent 100%
  );
}

/* Width variations for the fade effect */
.fade-narrow.sliding-text-faded-container {
  mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent 100%);
  -webkit-mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent 100%);
}

.fade-medium.sliding-text-faded-container {
  mask-image: linear-gradient(to right, transparent, black 10%, black 90%, transparent 100%);
  -webkit-mask-image: linear-gradient(
    to right,
    transparent,
    black 10%,
    black 90%,
    transparent 100%
  );
}

.fade-wide.sliding-text-faded-container {
  mask-image: linear-gradient(to right, transparent, black 15%, black 85%, transparent 100%);
  -webkit-mask-image: linear-gradient(
    to right,
    transparent,
    black 15%,
    black 85%,
    transparent 100%
  );
}

/* Font size scaling utility class */
.scaled-text {
  font-size: calc(1em * var(--font-size-scale));
}

.cuisina-block-header {
  color: #b52c34;
}

/* Partners Logos Animation */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* Partners Logos Animation Classes */
.animate-scroll-slow {
  animation: scroll-left 40s linear infinite;
}

.animate-scroll-medium {
  animation: scroll-left 30s linear infinite;
}

.animate-scroll-fast {
  animation: scroll-left 20s linear infinite;
}
