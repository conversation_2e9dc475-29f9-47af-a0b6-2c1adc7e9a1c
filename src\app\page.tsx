import { redirect } from 'next/navigation'
import { cookies } from 'next/headers'

// This page should never be reached due to middleware redirect
// But we include it as a fallback
export default async function RootPage() {
  const cookieStore = await cookies()
  const localePreference = cookieStore.get('locale-preference')?.value

  // Check if the locale preference is valid
  const validLocales = ['en', 'fr']
  const targetLocale = validLocales.includes(localePreference || '')
    ? localePreference
    : 'fr'

  // Redirect to the appropriate locale
  redirect(`/${targetLocale}`)
}
