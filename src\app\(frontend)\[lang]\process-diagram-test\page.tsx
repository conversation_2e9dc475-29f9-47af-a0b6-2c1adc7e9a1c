'use client'

import React from 'react'
import { ProcessDiagramBlock } from '@/blocks/ProcessDiagram/Component'
import { SimpleProcessDiagram } from './simple-component'
import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'

// Example rich text content for descriptions
const createRichText = (text: string): DefaultTypedEditorState => {
  return {
    root: {
      children: [
        {
          children: [
            {
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              text,
              type: 'text',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'paragraph',
          version: 1,
        },
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  }
}

export default function ProcessDiagramTestPage() {
  // Simple example process steps
  const processSteps = [
    {
      stepNumber: 1,
      title: 'Design Phase',
      description: createRichText('Initial design concepts are created based on market research and brand identity.'),
      technicalSpecs: {
        enabled: true,
        details: createRichText('- Design software: Adobe Illustrator\n- File formats: .ai, .pdf')
      },
      qualityControl: {
        enabled: true,
        checkpoints: [
          { name: 'Design Review', description: 'Evaluation by creative director' },
          { name: 'Brand Alignment', description: 'Verification that designs match brand guidelines' }
        ]
      }
    },
    {
      stepNumber: 2,
      title: 'Production Phase',
      description: createRichText('Converting designs into actual products through manufacturing processes.'),
      technicalSpecs: {
        enabled: true,
        details: createRichText('- Production capacity: 1,000 units/week\n- Quality standard: ISO 9001')
      },
      qualityControl: {
        enabled: true,
        checkpoints: [
          { name: 'Material Testing', description: 'Quality testing of all materials' },
          { name: 'Production Inspection', description: 'Regular quality checks during production' }
        ]
      }
    },
    {
      stepNumber: 3,
      title: 'Distribution Phase',
      description: createRichText('Getting the finished products to customers through various channels.'),
      technicalSpecs: {
        enabled: false
      },
      qualityControl: {
        enabled: true,
        checkpoints: [
          { name: 'Packaging Inspection', description: 'Ensuring proper packaging' },
          { name: 'Shipping Verification', description: 'Tracking and delivery confirmation' }
        ]
      }
    }
  ]

  return (
    <div className="py-8">
      <h1 className="text-3xl font-bold text-center mb-8">Process Diagram Test Page</h1>
      
      {/* Simple component to test basic rendering */}
      <div className="mb-16 border-b pb-16">
        <h2 className="text-2xl font-bold mb-8">Simple Component Test</h2>
        <SimpleProcessDiagram />
      </div>
      
      {/* Full ProcessDiagram component */}
      <div>
        <h2 className="text-2xl font-bold mb-8">Full Component Test</h2>
        <ProcessDiagramBlock
          heading="Simple Process Flow"
          subheading="A demonstration of the ProcessDiagram block"
          processSteps={processSteps}
          diagramLayout="linear-vertical"
          variant="default"
          colorScheme="primary"
          showStepConnectors={true}
          lineStyle="solid"
          headingAlignment="center"
          headerContentAlignment="center"
          enableParallax={false}
          highlightActiveStep={true}
          animateSteps={true}
          animationStyle="fade"
        />
      </div>
    </div>
  )
}
