import clsx from 'clsx'
import Image from 'next/image'
import React from 'react'

interface Props {
  className?: string
  loading?: 'lazy' | 'eager'
  priority?: 'auto' | 'high' | 'low'
  src?: string
}

export const Logo = (props: Props) => {
  const {
    loading: loadingFromProps,
    priority: priorityFromProps,
    className,
    src = '/logo.svg',
  } = props

  const loading = loadingFromProps || 'lazy'
  const priority = priorityFromProps || 'low'

  return (
    /* eslint-disable @next/next/no-img-element */
    <Image
      alt="Cuisina Logo"
      width={120}
      height={120}
      loading={loading}
      fetchPriority={priority}
      decoding="async"
      className={clsx('max-w-[7.5rem] w-full min-h-[40px]', className)}
      src={src}
    />
  )
}
