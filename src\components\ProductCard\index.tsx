'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowUpRight, Heart, Share2, Eye, Tag, Palette, Ruler } from 'lucide-react'
import Link from 'next/link'
import { cn } from '@/utilities/ui'
import { Media } from '@/components/Media'
import useClickableCard from '@/utilities/useClickableCard'

// Product type definition
type ProductCardData = {
  id: string
  title: string
  slug: string
  heroImage?: any
  categories?: Array<{
    id: string
    title: string
    slug: string
  }>
  format?: string
  dimensions?: {
    width?: number
    height?: number
    depth?: number
    weight?: number
  }
  materials?: Array<{
    material: string
    percentage?: number
  }>
  colors?: Array<{
    name: string
    hexCode?: string
  }>
  meta?: {
    title?: string | null
    description?: string | null
    image?: any
  } | null
}

interface ProductCardProps {
  product: ProductCardData
  locale?: string
  className?: string
  showQuickActions?: boolean
  variant?: 'default' | 'compact' | 'featured'
}

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  locale = 'en',
  className,
  showQuickActions = true,
  variant = 'default'
}) => {
  const { card, link } = useClickableCard({})
  const [isHovered, setIsHovered] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)

  const { title, slug, heroImage, categories, format, dimensions, materials, colors, meta } = product
  const { description, image: metaImage } = meta || {}
  const imageToUse = metaImage || heroImage

  const href = `/${locale}/products/${slug}`

  // Get primary category
  const primaryCategory = categories && categories.length > 0 ? categories[0] : null

  // Format options display
  const getFormatDisplay = (format: string) => {
    const formatLabels = {
      'u-shaped': locale === 'fr' ? 'Cuisine en U' : 'U-shaped Kitchen',
      'l-shaped': locale === 'fr' ? 'Cuisine en L' : 'L-shaped Kitchen',
      'central-island': locale === 'fr' ? 'Cuisine avec îlot central' : 'Kitchen with Central Island'
    }
    return formatLabels[format as keyof typeof formatLabels] || format
  }

  // Get primary material
  const primaryMaterial = materials && materials.length > 0 ? materials[0] : null

  // Get primary color
  const primaryColor = colors && colors.length > 0 ? colors[0] : null

  const cardVariants = {
    default: 'aspect-[4/3]',
    compact: 'aspect-[3/2]',
    featured: 'aspect-[16/10]'
  }

  return (
    <motion.div
      ref={card.ref}
      className={cn(
        'group relative bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden',
        'hover:shadow-xl hover:shadow-red-500/10 hover:border-red-200',
        'transition-all duration-500 cursor-pointer',
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ y: -8 }}
      transition={{ duration: 0.4, ease: 'easeOut' }}
    >
      {/* Image Container */}
      <div className={cn('relative w-full overflow-hidden', cardVariants[variant])}>
        {imageToUse ? (
          <div className="relative w-full h-full">
            <Media
              resource={imageToUse}
              size="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              fill={true}
              imgClassName="object-cover transition-transform duration-700 group-hover:scale-110"
              priority={false}
            />

            {/* Gradient Overlay */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            {/* Product Badge */}
            <div className="absolute top-4 left-4">
              <div className="bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-lg">
                <span className="text-xs font-semibold text-red-600 uppercase tracking-wide">
                  {locale === 'fr' ? 'Produit' : 'Product'}
                </span>
              </div>
            </div>

            {/* Category Badge */}
            {primaryCategory && (
              <div className="absolute top-4 right-4">
                <div className="bg-red-500 text-white px-3 py-1.5 rounded-full shadow-lg">
                  <span className="text-xs font-semibold uppercase tracking-wide">
                    {primaryCategory.title}
                  </span>
                </div>
              </div>
            )}

            {/* Quick Actions */}
            {showQuickActions && (
              <AnimatePresence>
                {isHovered && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 20 }}
                    transition={{ duration: 0.3 }}
                    className="absolute bottom-4 right-4 flex gap-2"
                  >
                    <button
                      onClick={(e) => {
                        e.preventDefault()
                        setIsFavorited(!isFavorited)
                      }}
                      className={cn(
                        'w-10 h-10 rounded-full backdrop-blur-sm shadow-lg transition-all duration-200',
                        'flex items-center justify-center',
                        isFavorited
                          ? 'bg-red-500 text-white'
                          : 'bg-white/90 text-gray-700 hover:bg-red-500 hover:text-white'
                      )}
                    >
                      <Heart className={cn('w-4 h-4', isFavorited && 'fill-current')} />
                    </button>

                    <button className="w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-gray-700 hover:bg-white hover:text-red-500 transition-all duration-200">
                      <Share2 className="w-4 h-4" />
                    </button>

                    <Link
                      href={href}
                      ref={link.ref}
                      className="w-10 h-10 bg-red-500 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center text-white hover:bg-red-600 transition-all duration-200"
                    >
                      <Eye className="w-4 h-4" />
                    </Link>
                  </motion.div>
                )}
              </AnimatePresence>
            )}

            {/* Format Badge (for kitchen products) */}
            {format && (
              <div className="absolute bottom-4 left-4">
                <div className="bg-orange-500 text-white px-3 py-1.5 rounded-full shadow-lg">
                  <span className="text-xs font-semibold">
                    {getFormatDisplay(format)}
                  </span>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-100 via-gray-50 to-white flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="w-16 h-16 mx-auto mb-3 bg-gray-200 rounded-2xl flex items-center justify-center">
                <div className="w-8 h-8 bg-gray-300 rounded-xl"></div>
              </div>
              <p className="text-sm font-medium">
                {locale === 'fr' ? 'Aucune image' : 'No Image'}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Title */}
        <div className="mb-3">
          <h3 className="text-xl font-bold text-gray-900 leading-tight group-hover:text-red-600 transition-colors duration-300 line-clamp-2">
            <Link href={href} className="block">
              {title}
            </Link>
          </h3>
        </div>

        {/* Description */}
        {description && (
          <div className="mb-4">
            <p className="text-gray-600 leading-relaxed text-sm line-clamp-2">
              {description.replace(/\s/g, ' ')}
            </p>
          </div>
        )}

        {/* Product Specifications */}
        <div className="space-y-3 mb-4">
          {/* Materials */}
          {primaryMaterial && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Tag className="w-4 h-4 text-green-500" />
              <span className="font-medium">{primaryMaterial.material}</span>
              {primaryMaterial.percentage && (
                <span className="text-gray-400">({primaryMaterial.percentage}%)</span>
              )}
            </div>
          )}

          {/* Colors */}
          {primaryColor && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Palette className="w-4 h-4 text-blue-500" />
              <span className="font-medium">{primaryColor.name}</span>
              {primaryColor.hexCode && (
                <div
                  className="w-4 h-4 rounded-full border-2 border-white shadow-sm"
                  style={{ backgroundColor: primaryColor.hexCode }}
                />
              )}
            </div>
          )}

          {/* Dimensions */}
          {dimensions && (dimensions.width || dimensions.height) && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Ruler className="w-4 h-4 text-purple-500" />
              <span className="font-medium">
                {dimensions.width && dimensions.height
                  ? `${dimensions.width} × ${dimensions.height} cm`
                  : dimensions.width
                    ? `${dimensions.width} cm ${locale === 'fr' ? 'largeur' : 'width'}`
                    : `${dimensions.height} cm ${locale === 'fr' ? 'hauteur' : 'height'}`
                }
              </span>
            </div>
          )}
        </div>

        {/* Action Button */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {categories && categories.length > 1 && (
              <span className="text-xs text-gray-500">
                +{categories.length - 1} {locale === 'fr' ? 'catégories' : 'categories'}
              </span>
            )}
          </div>

          <motion.div
            className="flex items-center gap-2 text-red-600 font-semibold text-sm opacity-0 group-hover:opacity-100 transition-all duration-300"
            animate={{ x: isHovered ? 4 : 0 }}
          >
            <span>{locale === 'fr' ? 'Voir le produit' : 'View Product'}</span>
            <ArrowUpRight className="w-4 h-4" />
          </motion.div>
        </div>
      </div>
    </motion.div>
  )
}
